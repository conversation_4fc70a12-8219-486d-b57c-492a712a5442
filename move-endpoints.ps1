# PowerShell script to move all endpoints to feature-based structure

# Define the mappings from old endpoints to new feature folders
$endpointMappings = @{
    "AcademicCalendarEndpoints.cs" = "AcademicCalendar"
    "AlumniEndpoints.cs" = "Alumni"
    "CalendarEndpoints.cs" = "Calendar"
    "CareerEndpoints.cs" = "Career"
    "ClubEndpoints.cs" = "Club"
    "ContentEndpoints.cs" = "Content"
    "EventEndpoints.cs" = "Event"
    "HealthCheckEndpoints.cs" = "HealthCheck"
    "HolidayEndpoints.cs" = "Holiday"
    "HostelFacilityEndpoints.cs" = "HostelFacility"
    "MediaEndpoints.cs" = "Media"
    "NoticeEndpoints.cs" = "Notice"
    "ParentEndpoints.cs" = "Parent"
    "StudentEndpoints.cs" = "Student"
    "TermEndpoints.cs" = "Term"
    "TuitionFeeEndpoints.cs" = "TuitionFee"
}

# Define the mappings from controllers to feature folders
$controllerMappings = @{
    "ClassTeacherController.cs" = "ClassTeacher"
    "GradeController.cs" = "Grade"
    "SectionController.cs" = "Section"
}

$sourceEndpointsPath = "src/School.API/Endpoints"
$sourceControllersPath = "src/School.API/Controllers"
$targetFeaturesPath = "src/School.API/Features"

# Function to move and update endpoint file
function Move-EndpointFile {
    param(
        [string]$sourceFile,
        [string]$targetFeature,
        [string]$sourceDir
    )
    
    $sourcePath = Join-Path $sourceDir $sourceFile
    $targetDir = Join-Path $targetFeaturesPath $targetFeature
    $targetPath = Join-Path $targetDir ($sourceFile -replace "Controller\.cs$", "Endpoints.cs")
    
    # Create target directory if it doesn't exist
    if (!(Test-Path $targetDir)) {
        New-Item -ItemType Directory -Path $targetDir -Force
    }
    
    # Read the source file content
    if (Test-Path $sourcePath) {
        $content = Get-Content $sourcePath -Raw
        
        # Update namespace
        $content = $content -replace "namespace School\.API\.Endpoints;", "namespace School.API.Features.$targetFeature;"
        $content = $content -replace "namespace School\.API\.Controllers;", "namespace School.API.Features.$targetFeature;"
        
        # Write to target location
        Set-Content -Path $targetPath -Value $content
        
        Write-Host "Moved $sourceFile to $targetFeature feature"
        
        # Remove source file
        Remove-Item $sourcePath -Force
    }
}

# Move endpoint files
foreach ($mapping in $endpointMappings.GetEnumerator()) {
    Move-EndpointFile -sourceFile $mapping.Key -targetFeature $mapping.Value -sourceDir $sourceEndpointsPath
}

# Move controller files
foreach ($mapping in $controllerMappings.GetEnumerator()) {
    Move-EndpointFile -sourceFile $mapping.Key -targetFeature $mapping.Value -sourceDir $sourceControllersPath
}

Write-Host "All endpoints and controllers have been moved to feature-based structure!"
