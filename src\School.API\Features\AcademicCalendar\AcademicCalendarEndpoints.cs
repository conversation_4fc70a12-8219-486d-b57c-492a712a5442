using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.AcademicCalendar;

namespace School.API.Features.AcademicCalendar;

public class AcademicCalendarEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/academic-calendar").WithTags("Academic Calendar");

        // Get all academic calendars with filtering and pagination
        group.MapGet("/", async ([AsParameters] AcademicCalendarFilterDto filter, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var (academicCalendars, totalCount) = await academicCalendarService.GetAllAcademicCalendarsAsync(filter);
            var response = new { TotalCount = totalCount, Items = academicCalendars };
            return ApiResults.ApiOk(response, "Academic calendars retrieved successfully");
        }).WithName("GetAllAcademicCalendars").WithOpenApi();

        // Get academic calendar by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var academicCalendar = await academicCalendarService.GetAcademicCalendarByIdAsync(id);
            if (academicCalendar == null)
            {
                return ApiResults.ApiNotFound("Academic calendar not found");
            }
            return ApiResults.ApiOk(academicCalendar, "Academic calendar retrieved successfully");
        }).WithName("GetAcademicCalendarById").WithOpenApi();

        // Get calendar events
        group.MapGet("/events", async ([FromServices] IAcademicCalendarService academicCalendarService, [FromQuery] int academicYear = 0, [FromQuery] string? semester = null) =>
        {
            var events = await academicCalendarService.GetCalendarEventsAsync(academicYear, semester);
            return ApiResults.ApiOk(events, "Calendar events retrieved successfully");
        }).WithName("GetCalendarEvents").WithOpenApi();

        // Create new academic calendar
        group.MapPost("/", async ([FromBody] CreateAcademicCalendarDto academicCalendarDto, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var academicCalendarId = await academicCalendarService.CreateAcademicCalendarAsync(academicCalendarDto);
            return ApiResults.ApiCreated($"/api/academic-calendar/{academicCalendarId}", academicCalendarId.ToString(), "Academic calendar created successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateAcademicCalendar")
        .WithOpenApi();

        // Update academic calendar
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateAcademicCalendarDto academicCalendarDto, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var result = await academicCalendarService.UpdateAcademicCalendarAsync(id, academicCalendarDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Academic calendar not found");
            }
            return ApiResults.ApiOk("Academic calendar updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateAcademicCalendar")
        .WithOpenApi();

        // Delete academic calendar
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var result = await academicCalendarService.DeleteAcademicCalendarAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Academic calendar not found");
            }
            return ApiResults.ApiOk("Academic calendar deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteAcademicCalendar")
        .WithOpenApi();

        // Translation endpoints
        var translationGroup = group.MapGroup("/{academicCalendarId}/translations");

        // Add translation
        translationGroup.MapPost("/", async ([FromRoute] Guid academicCalendarId, [FromBody] CreateAcademicCalendarTranslationDto translationDto, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var result = await academicCalendarService.AddTranslationAsync(academicCalendarId, translationDto);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Translation already exists or academic calendar not found");
            }
            return ApiResults.ApiOk("Translation added successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("AddAcademicCalendarTranslation")
        .WithOpenApi();

        // Update translation
        translationGroup.MapPut("/{languageCode}", async ([FromRoute] Guid academicCalendarId, [FromRoute] string languageCode, [FromBody] UpdateAcademicCalendarTranslationDto translationDto, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var result = await academicCalendarService.UpdateTranslationAsync(academicCalendarId, languageCode, translationDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateAcademicCalendarTranslation")
        .WithOpenApi();

        // Delete translation
        translationGroup.MapDelete("/{languageCode}", async ([FromRoute] Guid academicCalendarId, [FromRoute] string languageCode, [FromServices] IAcademicCalendarService academicCalendarService) =>
        {
            var result = await academicCalendarService.DeleteTranslationAsync(academicCalendarId, languageCode);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteAcademicCalendarTranslation")
        .WithOpenApi();
    }
}
