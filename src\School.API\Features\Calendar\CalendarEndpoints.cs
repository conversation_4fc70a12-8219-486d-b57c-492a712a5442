using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.Application.DTOs;
using School.Application.Features.AcademicCalendar;
using School.Domain.Enums;

namespace School.API.Features.Calendar;

public class CalendarEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/calendar")
            .WithTags("Calendar")
            .RequireAuthorization();

        // Enhanced Calendar Events
        group.MapGet("/events", GetIntegratedCalendarEvents)
            .WithName("GetIntegratedCalendarEvents")
            .WithSummary("Get integrated calendar events (academic events + holidays)")
            .WithDescription("Retrieves calendar events including both academic calendar events and holidays for a specified date range")
            .Produces<IEnumerable<CalendarEventDto>>();

        group.MapGet("/events/academic-year/{academicYearId:guid}", GetAcademicYearCalendar)
            .WithName("GetAcademicYearCalendar")
            .WithSummary("Get calendar events for a specific academic year")
            .WithDescription("Retrieves all calendar events (academic + holidays) for a specific academic year")
            .Produces<IEnumerable<CalendarEventDto>>();

        group.MapGet("/events/term/{termId:guid}", GetTermCalendar)
            .WithName("GetTermCalendar")
            .WithSummary("Get calendar events for a specific term")
            .WithDescription("Retrieves all calendar events (academic + holidays) for a specific term")
            .Produces<IEnumerable<CalendarEventDto>>();

        group.MapGet("/events/current", GetCurrentAcademicYearCalendar)
            .WithName("GetCurrentAcademicYearCalendar")
            .WithSummary("Get calendar events for current academic year")
            .WithDescription("Retrieves all calendar events (academic + holidays) for the current academic year")
            .Produces<IEnumerable<CalendarEventDto>>();

        // Calendar Statistics
        group.MapGet("/statistics", GetCalendarStatistics)
            .WithName("GetCalendarStatistics")
            .WithSummary("Get calendar statistics")
            .WithDescription("Retrieves comprehensive statistics about calendar events and holidays")
            .Produces<CalendarStatisticsDto>();

        group.MapGet("/statistics/count", GetTotalEventsInPeriod)
            .WithName("GetTotalEventsInPeriod")
            .WithSummary("Get total events count in period")
            .WithDescription("Gets the total count of events (academic + holidays) in a specified date range")
            .Produces<int>();

        group.MapGet("/statistics/distribution", GetEventTypeDistribution)
            .WithName("GetEventTypeDistribution")
            .WithSummary("Get event type distribution")
            .WithDescription("Gets the distribution of events by type")
            .Produces<Dictionary<string, int>>();

        // Calendar Validation
        group.MapPost("/validate", ValidateEventDates)
            .WithName("ValidateEventDates")
            .WithSummary("Validate event dates")
            .WithDescription("Validates if event dates are valid and don't conflict with existing events")
            .Produces<bool>();

        group.MapGet("/overlapping", GetOverlappingEvents)
            .WithName("GetOverlappingEvents")
            .WithSummary("Get overlapping events")
            .WithDescription("Gets events that overlap with the specified date range")
            .Produces<IEnumerable<AcademicCalendarDto>>();

        // Legacy Academic Calendar Events (for backward compatibility)
        group.MapGet("/academic-events", GetAcademicCalendarEvents)
            .WithName("GetAcademicCalendarEvents")
            .WithSummary("Get academic calendar events (legacy)")
            .WithDescription("Legacy endpoint for academic calendar events only")
            .Produces<IEnumerable<AcademicCalendarEventDto>>();
    }

    private static async Task<IResult> GetIntegratedCalendarEvents(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] Guid? academicYearId,
        [FromQuery] Guid? termId,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var events = await calendarService.GetIntegratedCalendarEventsAsync(startDate, endDate, academicYearId, termId);
            return Results.Ok(events);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving calendar events: {ex.Message}");
        }
    }

    private static async Task<IResult> GetAcademicYearCalendar(
        Guid academicYearId,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var events = await calendarService.GetAcademicYearCalendarAsync(academicYearId);
            return Results.Ok(events);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving academic year calendar: {ex.Message}");
        }
    }

    private static async Task<IResult> GetTermCalendar(
        Guid termId,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var events = await calendarService.GetTermCalendarAsync(termId);
            return Results.Ok(events);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving term calendar: {ex.Message}");
        }
    }

    private static async Task<IResult> GetCurrentAcademicYearCalendar(
        IAcademicCalendarService calendarService)
    {
        try
        {
            var events = await calendarService.GetCurrentAcademicYearCalendarAsync();
            return Results.Ok(events);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving current academic year calendar: {ex.Message}");
        }
    }

    private static async Task<IResult> GetCalendarStatistics(
        [FromQuery] Guid? academicYearId,
        [FromQuery] Guid? termId,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var statistics = await calendarService.GetCalendarStatisticsAsync(academicYearId, termId);
            return Results.Ok(statistics);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving calendar statistics: {ex.Message}");
        }
    }

    private static async Task<IResult> GetTotalEventsInPeriod(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] Guid? academicYearId,
        [FromQuery] Guid? termId,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var count = await calendarService.GetTotalEventsInPeriodAsync(startDate, endDate, academicYearId, termId);
            return Results.Ok(count);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving event count: {ex.Message}");
        }
    }

    private static async Task<IResult> GetEventTypeDistribution(
        [FromQuery] Guid? academicYearId,
        [FromQuery] Guid? termId,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var distribution = await calendarService.GetEventTypeDistributionAsync(academicYearId, termId);
            return Results.Ok(distribution);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving event type distribution: {ex.Message}");
        }
    }

    private static async Task<IResult> ValidateEventDates(
        [FromBody] ValidateEventDatesRequest request,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var isValid = await calendarService.ValidateEventDatesAsync(
                request.AcademicYearId, 
                request.TermId, 
                request.StartDate, 
                request.EndDate, 
                request.ExcludeId);
            return Results.Ok(isValid);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error validating event dates: {ex.Message}");
        }
    }

    private static async Task<IResult> GetOverlappingEvents(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] Guid? academicYearId,
        [FromQuery] Guid? termId,
        [FromQuery] Guid? excludeId,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var events = await calendarService.GetOverlappingEventsAsync(academicYearId, termId, startDate, endDate, excludeId);
            return Results.Ok(events);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving overlapping events: {ex.Message}");
        }
    }

    private static async Task<IResult> GetAcademicCalendarEvents(
        [FromQuery] int academicYear,
        [FromQuery] string? semester,
        IAcademicCalendarService calendarService)
    {
        try
        {
            var events = await calendarService.GetCalendarEventsAsync(academicYear, semester);
            return Results.Ok(events);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving academic calendar events: {ex.Message}");
        }
    }
}

// Request DTOs
public class ValidateEventDatesRequest
{
    public Guid? AcademicYearId { get; set; }
    public Guid? TermId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public Guid? ExcludeId { get; set; }
}
