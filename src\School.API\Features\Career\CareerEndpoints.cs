using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.Career;
using School.Domain.Enums;

namespace School.API.Features.Career;

public class CareerEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/careers").WithTags("Careers");

        // Get all careers with filtering and pagination
        group.MapGet("/", async ([AsParameters] CareerFilterDto filter, [FromServices] ICareerService careerService) =>
        {
            var (careers, totalCount) = await careerService.GetAllCareersAsync(filter);
            var response = new { TotalCount = totalCount, Items = careers };
            return ApiResults.ApiOk(response, "Careers retrieved successfully");
        }).WithName("GetAllCareers").WithOpenApi();

        // Get career by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] ICareerService careerService) =>
        {
            var career = await careerService.GetCareerByIdAsync(id);
            if (career == null)
            {
                return ApiResults.ApiNotFound("Career not found");
            }
            return ApiResults.ApiOk(career, "Career retrieved successfully");
        }).WithName("GetCareerById").WithOpenApi();

        // Create new career
        group.MapPost("/", async ([FromBody] CreateCareerDto careerDto, [FromServices] ICareerService careerService) =>
        {
            var careerId = await careerService.CreateCareerAsync(careerDto);
            return ApiResults.ApiCreated($"/api/careers/{careerId}", careerId.ToString(), "Career created successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateCareer")
        .WithOpenApi();

        // Update career
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateCareerDto careerDto, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.UpdateCareerAsync(id, careerDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Career not found");
            }
            return ApiResults.ApiOk("Career updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateCareer")
        .WithOpenApi();

        // Delete career
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.DeleteCareerAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Career not found");
            }
            return ApiResults.ApiOk("Career deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteCareer")
        .WithOpenApi();

        // Update career status
        group.MapPut("/{id}/status", async ([FromRoute] Guid id, [FromBody] CareerStatus status, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.UpdateCareerStatusAsync(id, status);
            if (!result)
            {
                return ApiResults.ApiNotFound("Career not found");
            }
            return ApiResults.ApiOk("Career status updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateCareerStatus")
        .WithOpenApi();

        // Translation endpoints
        var translationGroup = group.MapGroup("/{careerId}/translations");

        // Add translation
        translationGroup.MapPost("/", async ([FromRoute] Guid careerId, [FromBody] CreateCareerTranslationDto translationDto, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.AddTranslationAsync(careerId, translationDto);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Translation already exists or career not found");
            }
            return ApiResults.ApiOk("Translation added successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("AddCareerTranslation")
        .WithOpenApi();

        // Update translation
        translationGroup.MapPut("/{languageCode}", async ([FromRoute] Guid careerId, [FromRoute] string languageCode, [FromBody] UpdateCareerTranslationDto translationDto, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.UpdateTranslationAsync(careerId, languageCode, translationDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateCareerTranslation")
        .WithOpenApi();

        // Delete translation
        translationGroup.MapDelete("/{languageCode}", async ([FromRoute] Guid careerId, [FromRoute] string languageCode, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.DeleteTranslationAsync(careerId, languageCode);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteCareerTranslation")
        .WithOpenApi();

        // Application endpoints
        var applicationGroup = group.MapGroup("/applications");

        // Get all applications with filtering and pagination
        applicationGroup.MapGet("/", async ([AsParameters] CareerApplicationFilterDto filter, [FromServices] ICareerService careerService) =>
        {
            var (applications, totalCount) = await careerService.GetCareerApplicationsAsync(filter);
            var response = new { TotalCount = totalCount, Items = applications };
            return ApiResults.ApiOk(response, "Applications retrieved successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("GetAllApplications")
        .WithOpenApi();

        // Get application by ID
        applicationGroup.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] ICareerService careerService) =>
        {
            var application = await careerService.GetApplicationByIdAsync(id);
            if (application == null)
            {
                return ApiResults.ApiNotFound("Application not found");
            }
            return ApiResults.ApiOk(application, "Application retrieved successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("GetApplicationById")
        .WithOpenApi();

        // Create new application
        applicationGroup.MapPost("/", async ([FromBody] CreateCareerApplicationDto applicationDto, [FromServices] ICareerService careerService) =>
        {
            try
            {
                var applicationId = await careerService.CreateApplicationAsync(applicationDto);
                return ApiResults.ApiCreated($"/api/careers/applications/{applicationId}", applicationId.ToString(), "Application submitted successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .WithName("CreateApplication")
        .WithOpenApi();

        // Update application status
        applicationGroup.MapPut("/{id}/status", async ([FromRoute] Guid id, [FromBody] UpdateCareerApplicationStatusDto statusDto, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.UpdateApplicationStatusAsync(id, statusDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Application not found");
            }
            return ApiResults.ApiOk("Application status updated successfully");
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateApplicationStatus")
        .WithOpenApi();

        // Delete application
        applicationGroup.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] ICareerService careerService) =>
        {
            var result = await careerService.DeleteApplicationAsync(id);
            if (!result)
            {
                return ApiResults.ApiNotFound("Application not found");
            }
            return ApiResults.ApiOk("Application deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteApplication")
        .WithOpenApi();
    }
}
