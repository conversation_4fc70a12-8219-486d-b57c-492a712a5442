using Carter;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using School.API.Common;
using School.Application.Common.Models;
using School.Application.DTOs;
using School.Application.Features.Clubs;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace School.API.Features.Club;

public class ClubEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/clubs")
            .WithTags("Clubs");

        // Get all clubs with filtering and pagination
        group.MapGet("/", async (
            [AsParameters] ClubFilterDto filter,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.GetClubsAsync(filter, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk(result.Data, "Clubs retrieved successfully");
        })
        .WithName("GetClubs")
        .WithDescription("Get all clubs with filtering and pagination")
        .Produces<ApiResponse<PagedList<ClubDto>>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // Get club by ID
        group.MapGet("/{id}", async (
            [FromRoute] Guid id,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.GetClubByIdAsync(id, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            if (result.Data == null)
            {
                return ApiResults.ApiNotFound("Club not found");
            }
            return ApiResults.ApiOk(result.Data, "Club retrieved successfully");
        })
        .WithName("GetClubById")
        .WithDescription("Get club by ID")
        .Produces<ApiResponse<ClubDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status400BadRequest);

        // Create new club
        group.MapPost("/", async (
            [FromBody] ClubCreateDto createClubDto,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.CreateClubAsync(createClubDto, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiCreated(result.Data, $"/api/clubs/{result.Data}", "Club created successfully");
        })
        .WithName("CreateClub")
        .WithDescription("Create a new club")
        .Produces<ApiResponse<Guid>>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest);

        // Update club
        group.MapPut("/{id}", async (
            [FromRoute] Guid id,
            [FromBody] ClubUpdateDto updateClubDto,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.UpdateClubAsync(id, updateClubDto, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk<object?>(null, "Club updated successfully");
        })
        .WithName("UpdateClub")
        .WithDescription("Update an existing club")
        .Produces<ApiResponse<object>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound);

        // Delete club
        group.MapDelete("/{id}", async (
            [FromRoute] Guid id,
            [FromServices] IClubService clubService,
            CancellationToken cancellationToken) =>
        {
            var result = await clubService.DeleteClubAsync(id, cancellationToken);
            if (!result.Success)
            {
                return ApiResults.ApiBadRequest(string.Join(", ", result.Errors));
            }
            return ApiResults.ApiOk<object?>(null, "Club deleted successfully");
        })
        .WithName("DeleteClub")
        .WithDescription("Delete a club")
        .Produces<ApiResponse<object>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound);

        // Note: Club member management endpoints removed as methods not available in service interface

        // Note: Add/Remove club member endpoints removed as methods not available in service interface
    }
}
