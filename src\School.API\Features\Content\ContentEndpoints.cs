using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Domain.Entities;
using School.Domain.Enums;
using School.Application.Features.Content;
using School.API.Common;

namespace School.API.Features.Content;

public class ContentEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/content").WithTags("Content");

        group.MapGet("/", async ([AsParameters] ContentFilterDto filter, [FromServices] IContentService contentService) =>
        {
            var (contents, totalCount) = await contentService.GetAllContentAsync(filter);
            var response = new { TotalCount = totalCount, Items = contents };
            return ApiResults.ApiOk(response, "Content retrieved successfully");
        }).WithName("GetAllContent").WithOpenApi();

        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IContentService contentService) =>
        {
            var content = await contentService.GetContentByIdAsync(id);
            if (content == null)
            {
                return ApiResults.ApiNotFound("Content not found");
            }
            return ApiResults.ApiOk(content, "Content retrieved successfully");
        }).WithName("GetContentById").WithOpenApi();

        group.MapGet("/slug/{slug}", async ([FromRoute] string slug, [FromServices] IContentService contentService) =>
        {
            var content = await contentService.GetContentBySlugAsync(slug);
            if (content == null)
            {
                return ApiResults.ApiNotFound("Content not found");
            }
            return ApiResults.ApiOk(content, "Content retrieved successfully");
        }).WithName("GetContentBySlug").WithOpenApi();

        group.MapPost("/", async ([FromBody] ContentCreateDto contentDto, [FromServices] IContentService contentService) =>
        {
            var contentId = await contentService.CreateContentAsync(contentDto);
            return ApiResults.ApiCreated($"/api/content/{contentId}", contentId.ToString(), "Content created successfully");
        }).WithName("CreateContent").WithOpenApi();

        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] ContentUpdateDto contentDto, [FromServices] IContentService contentService) =>
        {
            var updated = await contentService.UpdateContentAsync(id, contentDto);
            if (!updated)
            {
                return ApiResults.ApiNotFound("Content not found");
            }
            return ApiResults.ApiOk(id.ToString(), "Content updated successfully");
        }).WithName("UpdateContent").WithOpenApi();

        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IContentService contentService) =>
        {
            var deleted = await contentService.DeleteContentAsync(id);
            if (!deleted)
            {
                return ApiResults.ApiNotFound("Content not found");
            }
            return ApiResults.ApiNoContent();
        }).WithName("DeleteContent").WithOpenApi();
    }
}
