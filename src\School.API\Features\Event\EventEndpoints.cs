using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Domain.Enums;
using School.Application.Features.Event;
using System;
using System.Threading.Tasks;

namespace School.API.Features.Event;

public class EventEndpoints : ICarterModule
{

    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/events").WithTags("Events");

        group.MapGet("/", async ([AsParameters] EventFilterDto filter, [FromServices] IEventService eventService) =>
        {
            try
            {
                var (events, totalCount) = await eventService.GetAllEventsAsync(filter);
                var response = new { TotalCount = totalCount, Items = events };
                return ApiResults.ApiOk(response, "Events retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving events: {ex.Message}");
            }
        })
        .WithName("GetAllEvents")
        .WithOpenApi();

        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] IEventService eventService) =>
        {
            try
            {
                var eventDto = await eventService.GetEventByIdAsync(id);
                if (eventDto == null)
                {
                    return ApiResults.ApiNotFound("Event not found");
                }
                return ApiResults.ApiOk(eventDto, "Event retrieved successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error retrieving event: {ex.Message}");
            }
        })
        .WithName("GetEventById")
        .WithOpenApi();

        group.MapPost("/", async ([FromBody] CreateEventDto eventDto, [FromServices] IEventService eventService) =>
        {
            try
            {
                var eventId = await eventService.CreateEventAsync(eventDto);
                return ApiResults.ApiCreated($"/api/events/{eventId}", eventId.ToString(), "Event created successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error creating event: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("CreateEvent")
        .WithOpenApi();

        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateEventDto eventDto, [FromServices] IEventService eventService) =>
        {
            try
            {
                var updated = await eventService.UpdateEventAsync(id, eventDto);
                if (!updated)
                {
                    return ApiResults.ApiNotFound("Event not found");
                }
                return ApiResults.ApiOk(id.ToString(), "Event updated successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error updating event: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("UpdateEvent")
        .WithOpenApi();

        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] IEventService eventService) =>
        {
            try
            {
                var deleted = await eventService.DeleteEventAsync(id);
                if (!deleted)
                {
                    return ApiResults.ApiNotFound("Event not found");
                }
                return ApiResults.ApiOk(id.ToString(), "Event deleted successfully");
            }
            catch (Exception ex)
            {
                return ApiResults.ApiServerError($"Error deleting event: {ex.Message}");
            }
        })
        .RequireAuthorization("EditorPolicy")
        .WithName("DeleteEvent")
        .WithOpenApi();
    }
}
