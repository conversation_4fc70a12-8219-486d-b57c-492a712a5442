using <PERSON>;
using Microsoft.AspNetCore.Mvc;
using School.Application.Features.Subject;
using School.Application.Features.Subject.DTOs;

namespace School.API.Features.Subject;

/// <summary>
/// Extended endpoint implementations for SubjectController
/// </summary>
public partial class SubjectController
{
    #region Faculty-Subject Management

    private static async Task<IResult> GetFacultySubjects(
        Guid facultyId, 
        ISubjectService subjectService,
        [FromQuery] Guid? academicYearId = null)
    {
        try
        {
            var facultySubjects = await subjectService.GetFacultySubjectsAsync(facultyId, academicYearId);
            return Results.Ok(facultySubjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving faculty subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectFaculties(
        Guid subjectId, 
        ISubjectService subjectService,
        [FromQuery] Guid? academicYearId = null)
    {
        try
        {
            var subjectFaculties = await subjectService.GetSubjectFacultiesAsync(subjectId, academicYearId);
            return Results.Ok(subjectFaculties);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject faculties: {ex.Message}");
        }
    }

    private static async Task<IResult> AssignFacultyToSubject(
        CreateFacultySubjectDto facultySubjectDto, 
        ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.AssignFacultyToSubjectAsync(facultySubjectDto);
            return success ? Results.Created("", null) : Results.BadRequest("Failed to assign faculty to subject");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error assigning faculty to subject: {ex.Message}");
        }
    }

    #endregion

    #region Subject Analytics and Reporting

    private static async Task<IResult> GetSubjectSummary(ISubjectService subjectService)
    {
        try
        {
            var summary = await subjectService.GetSubjectSummaryAsync();
            return Results.Ok(summary);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject summary: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectsByType(int type, ISubjectService subjectService)
    {
        try
        {
            var subjectType = (Domain.Enums.SubjectType)type;
            var subjects = await subjectService.GetSubjectsByTypeAsync(subjectType);
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subjects by type: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectsByCategory(int category, ISubjectService subjectService)
    {
        try
        {
            var subjectCategory = (Domain.Enums.SubjectCategory)category;
            var subjects = await subjectService.GetSubjectsByCategoryAsync(subjectCategory);
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subjects by category: {ex.Message}");
        }
    }

    private static async Task<IResult> GetMandatorySubjects(ISubjectService subjectService)
    {
        try
        {
            var subjects = await subjectService.GetMandatorySubjectsAsync();
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving mandatory subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> GetOptionalSubjects(ISubjectService subjectService)
    {
        try
        {
            var subjects = await subjectService.GetOptionalSubjectsAsync();
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving optional subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> GetPracticalSubjects(ISubjectService subjectService)
    {
        try
        {
            var subjects = await subjectService.GetPracticalSubjectsAsync();
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving practical subjects: {ex.Message}");
        }
    }

    #endregion

    #region Subject Search

    private static async Task<IResult> SearchSubjects(string searchTerm, ISubjectService subjectService)
    {
        try
        {
            var subjects = await subjectService.SearchSubjectsAsync(searchTerm);
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error searching subjects: {ex.Message}");
        }
    }

    #endregion

    #region Bulk Operations

    private static async Task<IResult> BulkCreateSubjects(
        IEnumerable<CreateSubjectDto> subjects, 
        ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.BulkCreateSubjectsAsync(subjects);
            return success ? Results.Created("", null) : Results.BadRequest("Failed to create subjects");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error bulk creating subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> BulkActivateSubjects(
        IEnumerable<Guid> subjectIds, 
        ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.BulkActivateSubjectsAsync(subjectIds);
            return success ? Results.NoContent() : Results.BadRequest("Failed to activate subjects");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error bulk activating subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> BulkDeactivateSubjects(
        IEnumerable<Guid> subjectIds, 
        ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.BulkDeactivateSubjectsAsync(subjectIds);
            return success ? Results.NoContent() : Results.BadRequest("Failed to deactivate subjects");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error bulk deactivating subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> BulkDeleteSubjects(
        IEnumerable<Guid> subjectIds, 
        ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.BulkDeleteSubjectsAsync(subjectIds);
            return success ? Results.NoContent() : Results.BadRequest("Failed to delete subjects");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error bulk deleting subjects: {ex.Message}");
        }
    }

    #endregion

    #region Import/Export Operations

    private static async Task<IResult> ImportSubjectsFromCsv(
        IFormFile file, 
        ISubjectService subjectService)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return Results.BadRequest("No file provided");
            }

            using var stream = file.OpenReadStream();
            var success = await subjectService.ImportSubjectsFromCsvAsync(stream);
            return success ? Results.Created("", null) : Results.BadRequest("Failed to import subjects");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error importing subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> ExportSubjectsToCsv(
        ISubjectService subjectService,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int? category = null,
        [FromQuery] int? type = null,
        [FromQuery] bool? isActive = null)
    {
        try
        {
            var filter = new SubjectFilterDto
            {
                SearchTerm = searchTerm,
                Category = category.HasValue ? (Domain.Enums.SubjectCategory)category.Value : null,
                Type = type.HasValue ? (Domain.Enums.SubjectType)type.Value : null,
                IsActive = isActive,
                Page = 1,
                PageSize = int.MaxValue
            };

            var stream = await subjectService.ExportSubjectsToCsvAsync(filter);
            return Results.File(stream, "text/csv", "subjects.csv");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error exporting subjects to CSV: {ex.Message}");
        }
    }

    private static async Task<IResult> ExportSubjectsToExcel(
        ISubjectService subjectService,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int? category = null,
        [FromQuery] int? type = null,
        [FromQuery] bool? isActive = null)
    {
        try
        {
            var filter = new SubjectFilterDto
            {
                SearchTerm = searchTerm,
                Category = category.HasValue ? (Domain.Enums.SubjectCategory)category.Value : null,
                Type = type.HasValue ? (Domain.Enums.SubjectType)type.Value : null,
                IsActive = isActive,
                Page = 1,
                PageSize = int.MaxValue
            };

            var stream = await subjectService.ExportSubjectsToExcelAsync(filter);
            return Results.File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "subjects.xlsx");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error exporting subjects to Excel: {ex.Message}");
        }
    }

    #endregion

    #region Subject Prerequisites

    private static async Task<IResult> GetSubjectPrerequisites(Guid subjectId, ISubjectService subjectService)
    {
        try
        {
            var prerequisites = await subjectService.GetSubjectPrerequisitesAsync(subjectId);
            return Results.Ok(prerequisites);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject prerequisites: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectDependents(Guid subjectId, ISubjectService subjectService)
    {
        try
        {
            var dependents = await subjectService.GetSubjectDependentsAsync(subjectId);
            return Results.Ok(dependents);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject dependents: {ex.Message}");
        }
    }

    private static async Task<IResult> AddSubjectPrerequisite(
        Guid subjectId, 
        Guid prerequisiteId, 
        ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.AddSubjectPrerequisiteAsync(subjectId, prerequisiteId);
            return success ? Results.Created("", null) : Results.BadRequest("Failed to add prerequisite");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error adding subject prerequisite: {ex.Message}");
        }
    }

    private static async Task<IResult> RemoveSubjectPrerequisite(
        Guid subjectId, 
        Guid prerequisiteId, 
        ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.RemoveSubjectPrerequisiteAsync(subjectId, prerequisiteId);
            return success ? Results.NoContent() : Results.NotFound("Prerequisite not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error removing subject prerequisite: {ex.Message}");
        }
    }

    #endregion

    #region Subject Statistics

    private static async Task<IResult> GetSubjectStatisticsByType(ISubjectService subjectService)
    {
        try
        {
            var statistics = await subjectService.GetSubjectStatisticsByTypeAsync();
            return Results.Ok(statistics);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject statistics by type: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectStatisticsByCategory(ISubjectService subjectService)
    {
        try
        {
            var statistics = await subjectService.GetSubjectStatisticsByCategoryAsync();
            return Results.Ok(statistics);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject statistics by category: {ex.Message}");
        }
    }

    #endregion
}
