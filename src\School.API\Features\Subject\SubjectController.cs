using <PERSON>;
using Microsoft.AspNetCore.Mvc;
using School.Application.Features.Subject;
using School.Application.Features.Subject.DTOs;

namespace School.API.Features.Subject;

/// <summary>
/// Subject management API endpoints
/// </summary>
public partial class SubjectController : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/subjects")
            .WithTags("Subjects")
            .RequireAuthorization();

        // Subject CRUD operations
        group.MapGet("/", GetAllSubjects)
            .WithName("GetAllSubjects")
            .WithSummary("Get all subjects with filtering and pagination")
            .Produces<(IEnumerable<SubjectDto> Subjects, int TotalCount)>();

        group.MapGet("/{id:guid}", GetSubjectById)
            .WithName("GetSubjectById")
            .WithSummary("Get subject by ID")
            .Produces<SubjectDto>()
            .Produces(404);

        group.MapGet("/code/{code}", GetSubjectByCode)
            .WithName("GetSubjectByCode")
            .WithSummary("Get subject by code")
            .Produces<SubjectDto>()
            .Produces(404);

        group.MapPost("/", CreateSubject)
            .WithName("CreateSubject")
            .WithSummary("Create a new subject")
            .Produces<Guid>(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapPut("/{id:guid}", UpdateSubject)
            .WithName("UpdateSubject")
            .WithSummary("Update an existing subject")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapDelete("/{id:guid}", DeleteSubject)
            .WithName("DeleteSubject")
            .WithSummary("Delete a subject")
            .Produces(204)
            .Produces(400)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/activate", ActivateSubject)
            .WithName("ActivateSubject")
            .WithSummary("Activate a subject")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/{id:guid}/deactivate", DeactivateSubject)
            .WithName("DeactivateSubject")
            .WithSummary("Deactivate a subject")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        // Subject validation
        group.MapGet("/validate/code/{code}", ValidateSubjectCode)
            .WithName("ValidateSubjectCode")
            .WithSummary("Check if subject code is unique")
            .Produces<bool>();

        group.MapGet("/validate/name/{name}", ValidateSubjectName)
            .WithName("ValidateSubjectName")
            .WithSummary("Check if subject name is unique")
            .Produces<bool>();

        // Grade-Subject management
        group.MapGet("/grade/{gradeId:guid}", GetSubjectsByGrade)
            .WithName("GetSubjectsByGrade")
            .WithSummary("Get subjects assigned to a grade")
            .Produces<IEnumerable<SubjectDto>>();

        group.MapGet("/grade/{gradeId:guid}/assignments", GetGradeSubjects)
            .WithName("GetGradeSubjects")
            .WithSummary("Get grade-subject assignments")
            .Produces<IEnumerable<GradeSubjectDto>>();

        group.MapPost("/grade/assign", AssignSubjectToGrade)
            .WithName("AssignSubjectToGrade")
            .WithSummary("Assign subject to grade")
            .Produces(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapDelete("/grade/{gradeId:guid}/subject/{subjectId:guid}", UnassignSubjectFromGrade)
            .WithName("UnassignSubjectFromGrade")
            .WithSummary("Unassign subject from grade")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        // Faculty-Subject management
        group.MapGet("/faculty/{facultyId:guid}", GetFacultySubjects)
            .WithName("GetFacultySubjects")
            .WithSummary("Get subjects assigned to faculty")
            .Produces<IEnumerable<FacultySubjectDto>>();

        group.MapGet("/{subjectId:guid}/faculties", GetSubjectFaculties)
            .WithName("GetSubjectFaculties")
            .WithSummary("Get faculties assigned to subject")
            .Produces<IEnumerable<FacultySubjectDto>>();

        group.MapPost("/faculty/assign", AssignFacultyToSubject)
            .WithName("AssignFacultyToSubject")
            .WithSummary("Assign faculty to subject")
            .Produces(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        // Subject analytics and reporting
        group.MapGet("/summary", GetSubjectSummary)
            .WithName("GetSubjectSummary")
            .WithSummary("Get subject summary statistics")
            .Produces<SubjectSummaryDto>();

        group.MapGet("/type/{type}", GetSubjectsByType)
            .WithName("GetSubjectsByType")
            .WithSummary("Get subjects by type")
            .Produces<IEnumerable<SubjectDto>>();

        group.MapGet("/category/{category}", GetSubjectsByCategory)
            .WithName("GetSubjectsByCategory")
            .WithSummary("Get subjects by category")
            .Produces<IEnumerable<SubjectDto>>();

        group.MapGet("/mandatory", GetMandatorySubjects)
            .WithName("GetMandatorySubjects")
            .WithSummary("Get mandatory subjects")
            .Produces<IEnumerable<SubjectDto>>();

        group.MapGet("/optional", GetOptionalSubjects)
            .WithName("GetOptionalSubjects")
            .WithSummary("Get optional subjects")
            .Produces<IEnumerable<SubjectDto>>();

        group.MapGet("/practical", GetPracticalSubjects)
            .WithName("GetPracticalSubjects")
            .WithSummary("Get practical subjects")
            .Produces<IEnumerable<SubjectDto>>();

        // Subject search
        group.MapGet("/search/{searchTerm}", SearchSubjects)
            .WithName("SearchSubjects")
            .WithSummary("Search subjects by term")
            .Produces<IEnumerable<SubjectDto>>();

        // Bulk operations
        group.MapPost("/bulk/create", BulkCreateSubjects)
            .WithName("BulkCreateSubjects")
            .WithSummary("Create multiple subjects")
            .Produces(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/bulk/activate", BulkActivateSubjects)
            .WithName("BulkActivateSubjects")
            .WithSummary("Activate multiple subjects")
            .Produces(204)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapPatch("/bulk/deactivate", BulkDeactivateSubjects)
            .WithName("BulkDeactivateSubjects")
            .WithSummary("Deactivate multiple subjects")
            .Produces(204)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapDelete("/bulk/delete", BulkDeleteSubjects)
            .WithName("BulkDeleteSubjects")
            .WithSummary("Delete multiple subjects")
            .Produces(204)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        // Import/Export operations
        group.MapPost("/import/csv", ImportSubjectsFromCsv)
            .WithName("ImportSubjectsFromCsv")
            .WithSummary("Import subjects from CSV file")
            .Produces(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapGet("/export/csv", ExportSubjectsToCsv)
            .WithName("ExportSubjectsToCsv")
            .WithSummary("Export subjects to CSV file")
            .Produces<Stream>(200, "text/csv");

        group.MapGet("/export/excel", ExportSubjectsToExcel)
            .WithName("ExportSubjectsToExcel")
            .WithSummary("Export subjects to Excel file")
            .Produces<Stream>(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        // Subject prerequisites
        group.MapGet("/{subjectId:guid}/prerequisites", GetSubjectPrerequisites)
            .WithName("GetSubjectPrerequisites")
            .WithSummary("Get subject prerequisites")
            .Produces<IEnumerable<SubjectDto>>();

        group.MapGet("/{subjectId:guid}/dependents", GetSubjectDependents)
            .WithName("GetSubjectDependents")
            .WithSummary("Get subjects that depend on this subject")
            .Produces<IEnumerable<SubjectDto>>();

        group.MapPost("/{subjectId:guid}/prerequisites/{prerequisiteId:guid}", AddSubjectPrerequisite)
            .WithName("AddSubjectPrerequisite")
            .WithSummary("Add subject prerequisite")
            .Produces(201)
            .Produces(400)
            .RequireAuthorization("AdminPolicy");

        group.MapDelete("/{subjectId:guid}/prerequisites/{prerequisiteId:guid}", RemoveSubjectPrerequisite)
            .WithName("RemoveSubjectPrerequisite")
            .WithSummary("Remove subject prerequisite")
            .Produces(204)
            .Produces(404)
            .RequireAuthorization("AdminPolicy");

        // Subject statistics
        group.MapGet("/statistics/type", GetSubjectStatisticsByType)
            .WithName("GetSubjectStatisticsByType")
            .WithSummary("Get subject statistics by type")
            .Produces<Dictionary<string, int>>();

        group.MapGet("/statistics/category", GetSubjectStatisticsByCategory)
            .WithName("GetSubjectStatisticsByCategory")
            .WithSummary("Get subject statistics by category")
            .Produces<Dictionary<string, int>>();
    }

    #region Endpoint Implementations

    private static async Task<IResult> GetAllSubjects(
        ISubjectService subjectService,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int? category = null,
        [FromQuery] int? type = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? isMandatory = null,
        [FromQuery] bool? hasPractical = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string sortBy = "Name",
        [FromQuery] bool sortDescending = false)
    {
        try
        {
            var filter = new SubjectFilterDto
            {
                SearchTerm = searchTerm,
                Category = category.HasValue ? (Domain.Enums.SubjectCategory)category.Value : null,
                Type = type.HasValue ? (Domain.Enums.SubjectType)type.Value : null,
                IsActive = isActive,
                IsMandatory = isMandatory,
                HasPractical = hasPractical,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy,
                SortDescending = sortDescending
            };

            var result = await subjectService.GetAllSubjectsAsync(filter);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectById(Guid id, ISubjectService subjectService)
    {
        try
        {
            var subject = await subjectService.GetSubjectByIdAsync(id);
            return subject != null ? Results.Ok(subject) : Results.NotFound($"Subject with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectByCode(string code, ISubjectService subjectService)
    {
        try
        {
            var subject = await subjectService.GetSubjectByCodeAsync(code);
            return subject != null ? Results.Ok(subject) : Results.NotFound($"Subject with code {code} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subject: {ex.Message}");
        }
    }

    private static async Task<IResult> CreateSubject(CreateSubjectDto subjectDto, ISubjectService subjectService)
    {
        try
        {
            var subjectId = await subjectService.CreateSubjectAsync(subjectDto);
            return Results.Created($"/api/subjects/{subjectId}", subjectId);
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error creating subject: {ex.Message}");
        }
    }

    private static async Task<IResult> UpdateSubject(Guid id, UpdateSubjectDto subjectDto, ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.UpdateSubjectAsync(id, subjectDto);
            return success ? Results.NoContent() : Results.NotFound($"Subject with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error updating subject: {ex.Message}");
        }
    }

    private static async Task<IResult> DeleteSubject(Guid id, ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.DeleteSubjectAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Subject with ID {id} not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deleting subject: {ex.Message}");
        }
    }

    private static async Task<IResult> ActivateSubject(Guid id, ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.ActivateSubjectAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Subject with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error activating subject: {ex.Message}");
        }
    }

    private static async Task<IResult> DeactivateSubject(Guid id, ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.DeactivateSubjectAsync(id);
            return success ? Results.NoContent() : Results.NotFound($"Subject with ID {id} not found");
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error deactivating subject: {ex.Message}");
        }
    }

    private static async Task<IResult> ValidateSubjectCode(string code, [FromQuery] Guid? excludeId, ISubjectService subjectService)
    {
        try
        {
            var isUnique = await subjectService.IsSubjectCodeUniqueAsync(code, excludeId);
            return Results.Ok(isUnique);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error validating subject code: {ex.Message}");
        }
    }

    private static async Task<IResult> ValidateSubjectName(string name, [FromQuery] Guid? excludeId, ISubjectService subjectService)
    {
        try
        {
            var isUnique = await subjectService.IsSubjectNameUniqueAsync(name, excludeId);
            return Results.Ok(isUnique);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error validating subject name: {ex.Message}");
        }
    }

    private static async Task<IResult> GetSubjectsByGrade(Guid gradeId, ISubjectService subjectService)
    {
        try
        {
            var subjects = await subjectService.GetSubjectsByGradeAsync(gradeId);
            return Results.Ok(subjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving subjects by grade: {ex.Message}");
        }
    }

    private static async Task<IResult> GetGradeSubjects(Guid gradeId, ISubjectService subjectService)
    {
        try
        {
            var gradeSubjects = await subjectService.GetGradeSubjectsAsync(gradeId);
            return Results.Ok(gradeSubjects);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error retrieving grade subjects: {ex.Message}");
        }
    }

    private static async Task<IResult> AssignSubjectToGrade(CreateGradeSubjectDto gradeSubjectDto, ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.AssignSubjectToGradeAsync(gradeSubjectDto);
            return success ? Results.Created("", null) : Results.BadRequest("Failed to assign subject to grade");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error assigning subject to grade: {ex.Message}");
        }
    }

    private static async Task<IResult> UnassignSubjectFromGrade(Guid gradeId, Guid subjectId, ISubjectService subjectService)
    {
        try
        {
            var success = await subjectService.UnassignSubjectFromGradeAsync(gradeId, subjectId);
            return success ? Results.NoContent() : Results.NotFound("Grade-Subject assignment not found");
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error unassigning subject from grade: {ex.Message}");
        }
    }

    #endregion
}
