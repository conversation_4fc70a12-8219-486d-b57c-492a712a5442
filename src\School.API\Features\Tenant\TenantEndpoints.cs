using Carter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using School.API.Common;
using School.Application.Common.Interfaces;
using School.Application.DTOs;
using School.Application.Features.Auth;
using School.Domain.Entities;
using School.Domain.Enums;
using School.Infrastructure.Identity;

namespace School.API.Features.Tenant;

/// <summary>
/// Endpoints for testing and managing tenant functionality
/// </summary>
public class TenantEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/tenant")
            .WithTags("Tenant")
            .WithOpenApi();

        // Test endpoint to check current tenant context
        group.MapGet("/current", GetCurrentTenant)
            .WithName("GetCurrentTenant")
            .WithSummary("Get current tenant information")
            .WithDescription("Returns information about the current tenant based on the request context")
            .Produces<TenantInfoResponse>(200)
            .Produces(404)
            .ExcludeFromDescription(); // Exclude from API response middleware

        // Test endpoint to check if tenant exists
        group.MapGet("/check/{identifier}", CheckTenantExists)
            .WithName("CheckTenantExists")
            .WithSummary("Check if tenant exists")
            .WithDescription("Checks if a tenant exists by slug or domain")
            .Produces<TenantExistsResponse>(200);

        // Admin endpoint to list all tenants (for testing)
        group.MapGet("/list", ListTenants)
            .WithName("ListTenants")
            .WithSummary("List all tenants")
            .WithDescription("Returns a list of all tenants (admin only)")
            .RequireAuthorization()
            .Produces<List<TenantSummary>>(200);

        // Create new tenant (System Admin only)
        group.MapPost("/create", CreateTenant)
            .WithName("CreateTenant")
            .WithSummary("Create new tenant")
            .WithDescription("Creates a new tenant organization with admin user")
            .RequireAuthorization()
            .Produces<TenantInfoResponse>(201)
            .Produces<ValidationProblemDetails>(400);

        // Get tenant by ID
        group.MapGet("/{tenantId:guid}", GetTenantById)
            .WithName("GetTenantById")
            .WithSummary("Get tenant by ID")
            .WithDescription("Returns detailed information about a specific tenant")
            .RequireAuthorization()
            .Produces<TenantInfoResponse>(200)
            .Produces(404);

        // Update tenant
        group.MapPut("/{tenantId:guid}", UpdateTenant)
            .WithName("UpdateTenant")
            .WithSummary("Update tenant")
            .WithDescription("Updates tenant information")
            .RequireAuthorization()
            .Produces<TenantInfoResponse>(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Update tenant status
        group.MapPatch("/{tenantId:guid}/status", UpdateTenantStatus)
            .WithName("UpdateTenantStatus")
            .WithSummary("Update tenant status")
            .WithDescription("Activates or deactivates a tenant")
            .RequireAuthorization()
            .Produces(200)
            .Produces(404);

        // Grant user access to tenant
        group.MapPost("/{tenantId:guid}/grant-access", GrantTenantAccess)
            .WithName("GrantTenantAccess")
            .WithSummary("Grant user access to tenant")
            .WithDescription("Grants a user access to a specific tenant")
            .RequireAuthorization()
            .Produces(200)
            .Produces<ValidationProblemDetails>(400)
            .Produces(404);

        // Revoke user access from tenant
        group.MapDelete("/{tenantId:guid}/revoke-access/{userId}", RevokeTenantAccess)
            .WithName("RevokeTenantAccess")
            .WithSummary("Revoke user access from tenant")
            .WithDescription("Revokes a user's access from a specific tenant")
            .RequireAuthorization()
            .Produces(200)
            .Produces(404);

        // Get tenant users
        group.MapGet("/{tenantId:guid}/users", GetTenantUsers)
            .WithName("GetTenantUsers")
            .WithSummary("Get tenant users")
            .WithDescription("Gets all users with access to a specific tenant")
            .RequireAuthorization()
            .Produces<List<TenantUserAccess>>(200)
            .Produces(404);

        // Get tenant statistics
        group.MapGet("/{tenantId:guid}/stats", GetTenantStats)
            .WithName("GetTenantStats")
            .WithSummary("Get tenant statistics")
            .WithDescription("Gets comprehensive statistics for a specific tenant")
            .RequireAuthorization()
            .Produces<TenantStatsResponse>(200)
            .Produces(404);
    }

    private static async Task<IResult> GetCurrentTenant(ITenantService tenantService, HttpContext httpContext)
    {
        try
        {
            var currentTenant = await tenantService.GetCurrentTenantAsync();

            if (currentTenant == null)
            {
                // Return a simple 404 response for tenant detection
                httpContext.Response.StatusCode = 404;
                httpContext.Response.ContentType = "application/json";
                await httpContext.Response.WriteAsync("{\"message\":\"No tenant context found\"}");
                return Results.Empty;
            }

            var response = new TenantInfoResponse
            {
                Id = currentTenant.Id,
                Name = currentTenant.Name,
                Slug = currentTenant.Slug,
                DisplayName = currentTenant.DisplayName,
                Type = currentTenant.Type.ToString(),
                Status = currentTenant.Status.ToString(),
                IsActive = currentTenant.IsActive,
                IsTrialActive = currentTenant.IsTrialActive,
                TrialEndDate = currentTenant.TrialEndDate,
                CustomDomain = currentTenant.CustomDomain,
                DefaultLanguage = currentTenant.DefaultLanguage,
                TimeZone = currentTenant.TimeZone,
                Currency = currentTenant.Currency
            };

            // Return standard API response format
            var apiResponse = ApiResponse<TenantInfoResponse>.SuccessResponse(response);
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            // Return simple error response for tenant detection
            httpContext.Response.StatusCode = 500;
            httpContext.Response.ContentType = "application/json";
            await httpContext.Response.WriteAsync($"{{\"message\":\"Error getting current tenant: {ex.Message}\"}}");
            return Results.Empty;
        }
    }

    private static async Task<IResult> CheckTenantExists(string identifier, ITenantService tenantService)
    {
        try
        {
            var tenantBySlug = await tenantService.GetTenantBySlugAsync(identifier);
            var tenantByDomain = await tenantService.GetTenantByDomainAsync(identifier);
            
            var exists = tenantBySlug != null || tenantByDomain != null;
            var tenant = tenantBySlug ?? tenantByDomain;

            var response = new TenantExistsResponse
            {
                Exists = exists,
                Identifier = identifier,
                TenantId = tenant?.Id,
                TenantName = tenant?.Name,
                FoundBy = tenantBySlug != null ? "slug" : tenantByDomain != null ? "domain" : null
            };

            return Results.Ok(response);
        }
        catch (Exception ex)
        {
            return Results.Problem($"Error checking tenant: {ex.Message}");
        }
    }

    private static async Task<IResult> ListTenants(ITenantService tenantService, IApplicationDbContext context)
    {
        try
        {
            // This is a simple implementation for testing
            // In production, you'd want proper pagination and filtering
            var tenants = await context.Organizations
                .Where(o => o.IsActive)
                .Select(o => new TenantSummary
                {
                    Id = o.Id,
                    Name = o.Name,
                    Slug = o.Slug,
                    DisplayName = o.DisplayName,
                    Type = o.Type.ToString(),
                    Status = o.Status.ToString(),
                    CustomDomain = o.CustomDomain,
                    JoinedDate = o.JoinedDate,
                    IsTrialActive = o.IsTrialActive,
                    TrialEndDate = o.TrialEndDate
                })
                .Take(50) // Limit for testing
                .ToListAsync();

            var apiResponse = ApiResponse<List<TenantSummary>>.SuccessResponse(tenants);
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            // Log the full exception for debugging
            Console.WriteLine($"Error listing tenants: {ex}");
            var errorResponse = ApiResponse.ErrorResponse($"Error listing tenants: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> CreateTenant(
        [FromBody] CreateTenantRequest request,
        ITenantService tenantService,
        IApplicationDbContext context,
        IAuthService authService)
    {
        try
        {
            // Validate request
            if (string.IsNullOrWhiteSpace(request.Name) || string.IsNullOrWhiteSpace(request.Slug))
            {
                var errorResponse = ApiResponse.ErrorResponse("Name and Slug are required", 400);
                return Results.Json(errorResponse, statusCode: 400);
            }

            // Check if slug already exists
            var existingOrg = await context.Organizations
                .FirstOrDefaultAsync(o => o.Slug == request.Slug);
            if (existingOrg != null)
            {
                var errorResponse = ApiResponse.ErrorResponse($"Slug '{request.Slug}' is already taken", 400);
                return Results.Json(errorResponse, statusCode: 400);
            }

            // Parse trial end date if provided
            DateTime? trialEndDate = null;
            if (request.IsTrialActive && !string.IsNullOrWhiteSpace(request.TrialEndDate))
            {
                if (DateTime.TryParse(request.TrialEndDate, out var parsedDate))
                {
                    trialEndDate = parsedDate.ToUniversalTime();
                }
                else
                {
                    var errorResponse = ApiResponse.ErrorResponse("Invalid trial end date format", 400);
                    return Results.Json(errorResponse, statusCode: 400);
                }
            }

            // Parse organization type
            if (!Enum.TryParse<OrganizationType>(request.Type, out var orgType))
            {
                var errorResponse = ApiResponse.ErrorResponse($"Invalid organization type: {request.Type}", 400);
                return Results.Json(errorResponse, statusCode: 400);
            }

            // Create organization
            var organization = new Organization
            {
                Id = Guid.NewGuid(),
                Name = request.Name.Trim(),
                Slug = request.Slug.Trim().ToLowerInvariant(),
                DisplayName = request.DisplayName?.Trim() ?? request.Name.Trim(),
                Type = orgType,
                Status = OrganizationStatus.Active,
                IsActive = true,
                CustomDomain = string.IsNullOrWhiteSpace(request.CustomDomain) ? null : request.CustomDomain.Trim().ToLowerInvariant(),
                DefaultLanguage = request.DefaultLanguage ?? "en-US",
                TimeZone = request.TimeZone ?? "UTC",
                Currency = request.Currency ?? "USD",
                IsTrialActive = request.IsTrialActive,
                TrialEndDate = trialEndDate,
                JoinedDate = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System" // TODO: Get from current user context
            };

            context.Organizations.Add(organization);
            await context.SaveChangesAsync();

            // Create admin user if provided
            Guid? adminUserId = null;
            if (request.AdminUser != null)
            {
                try
                {
                    // Create the admin user
                    var userCreateDto = new UserCreateDto
                    {
                        Username = request.AdminUser.Username,
                        Email = request.AdminUser.Email,
                        Password = request.AdminUser.Password,
                        ConfirmPassword = request.AdminUser.Password, // Same as password for admin creation
                        FirstName = request.AdminUser.FirstName,
                        LastName = request.AdminUser.LastName,
                        Role = UserRole.Admin, // Set as Admin role for tenant administration
                        PreferredLanguage = organization.DefaultLanguage,
                        TimeZone = organization.TimeZone
                    };

                    adminUserId = await authService.RegisterAsync(userCreateDto);

                    // Add the admin user to the organization with Administrator role
                    var organizationUser = new OrganizationUser
                    {
                        OrganizationId = organization.Id,
                        UserId = adminUserId.ToString(),
                        Role = OrganizationRole.Administrator, // Highest role in the organization
                        IsActive = true,
                        JoinedDate = DateTime.UtcNow,
                        AcceptedDate = DateTime.UtcNow,
                        InvitedBy = "System", // System-created during tenant setup
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System"
                    };

                    context.OrganizationUsers.Add(organizationUser);
                    await context.SaveChangesAsync();

                    Console.WriteLine($"Created admin user {request.AdminUser.Username} for tenant {organization.Name}");
                }
                catch (Exception ex)
                {
                    // If user creation fails, we should clean up the organization
                    Console.WriteLine($"Failed to create admin user: {ex.Message}");

                    // Remove the organization since admin user creation failed
                    context.Organizations.Remove(organization);
                    await context.SaveChangesAsync();

                    var errorResponse = ApiResponse.ErrorResponse($"Failed to create admin user: {ex.Message}", 500);
                    return Results.Json(errorResponse, statusCode: 500);
                }
            }

            var response = new TenantInfoResponse
            {
                Id = organization.Id,
                Name = organization.Name,
                Slug = organization.Slug,
                DisplayName = organization.DisplayName,
                Type = organization.Type.ToString(),
                Status = organization.Status.ToString(),
                IsActive = organization.IsActive,
                IsTrialActive = organization.IsTrialActive,
                TrialEndDate = organization.TrialEndDate,
                CustomDomain = organization.CustomDomain,
                DefaultLanguage = organization.DefaultLanguage,
                TimeZone = organization.TimeZone,
                Currency = organization.Currency
            };

            var apiResponse = ApiResponse<TenantInfoResponse>.SuccessResponse(response, "Tenant created successfully");
            apiResponse.StatusCode = 201;
            return Results.Json(apiResponse, statusCode: 201);
        }
        catch (Exception ex)
        {
            // Log the full exception for debugging
            Console.WriteLine($"Error creating tenant: {ex}");
            var errorResponse = ApiResponse.ErrorResponse($"Error creating tenant: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> GetTenantById(
        [FromRoute] Guid tenantId,
        IApplicationDbContext context)
    {
        try
        {
            var organization = await context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId);

            if (organization == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("Tenant not found", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            var response = new TenantInfoResponse
            {
                Id = organization.Id,
                Name = organization.Name,
                Slug = organization.Slug,
                DisplayName = organization.DisplayName,
                Type = organization.Type.ToString(),
                Status = organization.Status.ToString(),
                IsActive = organization.IsActive,
                IsTrialActive = organization.IsTrialActive,
                TrialEndDate = organization.TrialEndDate,
                CustomDomain = organization.CustomDomain,
                DefaultLanguage = organization.DefaultLanguage,
                TimeZone = organization.TimeZone,
                Currency = organization.Currency
            };

            var apiResponse = ApiResponse<TenantInfoResponse>.SuccessResponse(response);
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            var errorResponse = ApiResponse.ErrorResponse($"Error getting tenant: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> UpdateTenant(
        [FromRoute] Guid tenantId,
        [FromBody] UpdateTenantRequest request,
        IApplicationDbContext context)
    {
        try
        {
            var organization = await context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId);

            if (organization == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("Tenant not found", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            // Update properties
            organization.Name = request.Name ?? organization.Name;
            organization.DisplayName = request.DisplayName ?? organization.DisplayName;
            organization.CustomDomain = request.CustomDomain;
            organization.DefaultLanguage = request.DefaultLanguage ?? organization.DefaultLanguage;
            organization.TimeZone = request.TimeZone ?? organization.TimeZone;
            organization.Currency = request.Currency ?? organization.Currency;

            if (request.Type != null)
            {
                organization.Type = Enum.Parse<OrganizationType>(request.Type);
            }

            await context.SaveChangesAsync();

            var response = new TenantInfoResponse
            {
                Id = organization.Id,
                Name = organization.Name,
                Slug = organization.Slug,
                DisplayName = organization.DisplayName,
                Type = organization.Type.ToString(),
                Status = organization.Status.ToString(),
                IsActive = organization.IsActive,
                IsTrialActive = organization.IsTrialActive,
                TrialEndDate = organization.TrialEndDate,
                CustomDomain = organization.CustomDomain,
                DefaultLanguage = organization.DefaultLanguage,
                TimeZone = organization.TimeZone,
                Currency = organization.Currency
            };

            var apiResponse = ApiResponse<TenantInfoResponse>.SuccessResponse(response, "Tenant updated successfully");
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            var errorResponse = ApiResponse.ErrorResponse($"Error updating tenant: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> UpdateTenantStatus(
        [FromRoute] Guid tenantId,
        [FromBody] UpdateTenantStatusRequest request,
        IApplicationDbContext context)
    {
        try
        {
            var organization = await context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId);

            if (organization == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("Tenant not found", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            organization.IsActive = request.IsActive;
            organization.Status = request.IsActive ? OrganizationStatus.Active : OrganizationStatus.Inactive;

            await context.SaveChangesAsync();

            var message = $"Tenant {(request.IsActive ? "activated" : "deactivated")} successfully";
            var apiResponse = ApiResponse.SuccessResponse(message);
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            var errorResponse = ApiResponse.ErrorResponse($"Error updating tenant status: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> GrantTenantAccess(
        [FromRoute] Guid tenantId,
        [FromBody] GrantTenantAccessRequest request,
        IApplicationDbContext context,
        UserManager<ApplicationUser> userManager)
    {
        try
        {
            // Validate request
            if (string.IsNullOrWhiteSpace(request.UserEmail))
            {
                var errorResponse = ApiResponse.ErrorResponse("User email is required", 400);
                return Results.Json(errorResponse, statusCode: 400);
            }

            // Check if tenant exists
            var tenant = await context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId && o.IsActive);
            if (tenant == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("Tenant not found or inactive", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            // Find user by email
            var user = await userManager.FindByEmailAsync(request.UserEmail);
            if (user == null)
            {
                var errorResponse = ApiResponse.ErrorResponse($"User with email '{request.UserEmail}' not found", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            // Check if user already has access to this tenant
            var existingAccess = await context.OrganizationUsers
                .FirstOrDefaultAsync(ou => ou.OrganizationId == tenantId && ou.UserId == user.Id);

            if (existingAccess != null)
            {
                if (existingAccess.IsActive)
                {
                    var errorResponse = ApiResponse.ErrorResponse("User already has active access to this tenant", 400);
                    return Results.Json(errorResponse, statusCode: 400);
                }
                else
                {
                    // Reactivate existing access
                    existingAccess.IsActive = true;
                    existingAccess.Role = Enum.Parse<OrganizationRole>(request.Role);
                    existingAccess.AcceptedDate = DateTime.UtcNow;
                    existingAccess.LastModifiedAt = DateTime.UtcNow;
                    existingAccess.LastModifiedBy = "System"; // TODO: Get from current user context
                }
            }
            else
            {
                // Create new organization user relationship
                var organizationUser = new OrganizationUser
                {
                    OrganizationId = tenantId,
                    UserId = user.Id,
                    Role = Enum.Parse<OrganizationRole>(request.Role),
                    IsActive = true,
                    JoinedDate = DateTime.UtcNow,
                    AcceptedDate = DateTime.UtcNow,
                    InvitedBy = "System", // TODO: Get from current user context
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                };

                context.OrganizationUsers.Add(organizationUser);
            }

            await context.SaveChangesAsync();

            // TODO: Send invitation email if requested
            if (request.SendInvitation)
            {
                // Implement email notification service
                // await _emailService.SendTenantInvitationAsync(user.Email, tenant.Name, request.Role);
            }

            var response = new
            {
                UserId = user.Id,
                UserEmail = user.Email,
                UserName = $"{user.FirstName} {user.LastName}",
                TenantId = tenantId,
                TenantName = tenant.Name,
                Role = request.Role,
                GrantedAt = DateTime.UtcNow
            };

            var apiResponse = ApiResponse<object>.SuccessResponse(response, "Tenant access granted successfully");
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            var errorResponse = ApiResponse.ErrorResponse($"Error granting tenant access: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> RevokeTenantAccess(
        [FromRoute] Guid tenantId,
        [FromRoute] string userId,
        IApplicationDbContext context)
    {
        try
        {
            // Check if tenant exists
            var tenant = await context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId && o.IsActive);
            if (tenant == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("Tenant not found or inactive", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            // Find the organization user relationship
            var organizationUser = await context.OrganizationUsers
                .FirstOrDefaultAsync(ou => ou.OrganizationId == tenantId && ou.UserId == userId);

            if (organizationUser == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("User does not have access to this tenant", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            // Deactivate the access (soft delete)
            organizationUser.IsActive = false;
            organizationUser.LastModifiedAt = DateTime.UtcNow;
            organizationUser.LastModifiedBy = "System"; // TODO: Get from current user context

            await context.SaveChangesAsync();

            var response = new
            {
                UserId = userId,
                TenantId = tenantId,
                TenantName = tenant.Name,
                RevokedAt = DateTime.UtcNow
            };

            var apiResponse = ApiResponse<object>.SuccessResponse(response, "Tenant access revoked successfully");
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            var errorResponse = ApiResponse.ErrorResponse($"Error revoking tenant access: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> GetTenantUsers(
        [FromRoute] Guid tenantId,
        IApplicationDbContext context,
        UserManager<ApplicationUser> userManager)
    {
        try
        {
            // Check if tenant exists
            var tenant = await context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId && o.IsActive);
            if (tenant == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("Tenant not found or inactive", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            // Get all active organization users
            var organizationUsers = await context.OrganizationUsers
                .Where(ou => ou.OrganizationId == tenantId && ou.IsActive)
                .ToListAsync();

            var tenantUsers = new List<TenantUserAccess>();

            foreach (var orgUser in organizationUsers)
            {
                var user = await userManager.FindByIdAsync(orgUser.UserId);
                if (user != null)
                {
                    tenantUsers.Add(new TenantUserAccess
                    {
                        UserId = user.Id,
                        UserEmail = user.Email ?? "",
                        UserName = $"{user.FirstName} {user.LastName}",
                        Role = orgUser.Role.ToString(),
                        IsActive = orgUser.IsActive,
                        GrantedAt = orgUser.JoinedDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        GrantedBy = orgUser.InvitedBy ?? "System"
                    });
                }
            }

            var apiResponse = ApiResponse<List<TenantUserAccess>>.SuccessResponse(tenantUsers);
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            var errorResponse = ApiResponse.ErrorResponse($"Error retrieving tenant users: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }

    private static async Task<IResult> GetTenantStats(
        [FromRoute] Guid tenantId,
        IApplicationDbContext context)
    {
        try
        {
            // Check if tenant exists
            var tenant = await context.Organizations
                .FirstOrDefaultAsync(o => o.Id == tenantId && o.IsActive);
            if (tenant == null)
            {
                var errorResponse = ApiResponse.ErrorResponse("Tenant not found or inactive", 404);
                return Results.Json(errorResponse, statusCode: 404);
            }

            // Get comprehensive tenant statistics
            var stats = new TenantStatsResponse
            {
                TenantId = tenantId,
                TenantName = tenant.Name,
                TotalUsers = await context.OrganizationUsers
                    .CountAsync(ou => ou.OrganizationId == tenantId && ou.IsActive),
                TotalStudents = await context.Students
                    .CountAsync(s => s.TenantId == tenantId && s.IsActive),
                TotalFaculty = await context.Faculty
                    .CountAsync(f => f.TenantId == tenantId && f.IsActive),
                TotalParents = await context.Parents
                    .CountAsync(p => p.TenantId == tenantId && p.IsActive),
                TotalGrades = await context.Grades
                    .CountAsync(g => g.TenantId == tenantId && g.IsActive),
                TotalSections = await context.Sections
                    .CountAsync(s => s.TenantId == tenantId && s.IsActive),
                TotalSubjects = await context.Subjects
                    .CountAsync(s => s.TenantId == tenantId && s.IsActive),

                // Additional analytics
                TotalAcademicYears = await context.AcademicYears
                    .CountAsync(ay => ay.TenantId == tenantId && !ay.IsDeleted),
                TotalTerms = await context.Terms
                    .CountAsync(t => t.TenantId == tenantId && !t.IsDeleted),
                TotalNotices = await context.Notices
                    .CountAsync(n => n.TenantId == tenantId && n.IsActive),
                TotalEvents = await context.Events
                    .CountAsync(e => e.TenantId == tenantId && !e.IsDeleted),

                // Activity metrics
                ActiveStudentsThisMonth = await context.Students
                    .CountAsync(s => s.TenantId == tenantId && s.IsActive &&
                               s.LastModifiedAt >= DateTime.UtcNow.AddDays(-30)),
                NewStudentsThisMonth = await context.Students
                    .CountAsync(s => s.TenantId == tenantId && s.IsActive &&
                               s.CreatedAt >= DateTime.UtcNow.AddDays(-30)),

                // Trial and subscription info
                IsTrialActive = tenant.IsTrialActive,
                TrialEndDate = tenant.TrialEndDate,
                JoinedDate = tenant.JoinedDate,
                LastActivityDate = DateTime.UtcNow, // TODO: Implement actual last activity tracking
                StorageUsedMB = 0, // TODO: Implement storage calculation
                MaxStorageMB = tenant.MaxStorageMB,

                // Subscription limits
                MaxStudents = tenant.MaxStudents,
                MaxFaculty = tenant.MaxFaculty
            };

            var apiResponse = ApiResponse<TenantStatsResponse>.SuccessResponse(stats);
            return Results.Ok(apiResponse);
        }
        catch (Exception ex)
        {
            var errorResponse = ApiResponse.ErrorResponse($"Error retrieving tenant statistics: {ex.Message}", 500);
            return Results.Json(errorResponse, statusCode: 500);
        }
    }
}

// DTOs for tenant management
public class TenantUserAccess
{
    public string UserId { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public string GrantedAt { get; set; } = string.Empty;
    public string GrantedBy { get; set; } = string.Empty;
}

public class TenantStatsResponse
{
    public Guid TenantId { get; set; }
    public string TenantName { get; set; } = string.Empty;

    // User statistics
    public int TotalUsers { get; set; }
    public int TotalStudents { get; set; }
    public int TotalFaculty { get; set; }
    public int TotalParents { get; set; }

    // Academic structure statistics
    public int TotalGrades { get; set; }
    public int TotalSections { get; set; }
    public int TotalSubjects { get; set; }
    public int TotalAcademicYears { get; set; }
    public int TotalTerms { get; set; }

    // Content statistics
    public int TotalNotices { get; set; }
    public int TotalEvents { get; set; }

    // Activity metrics
    public int ActiveStudentsThisMonth { get; set; }
    public int NewStudentsThisMonth { get; set; }

    // Trial and subscription info
    public bool IsTrialActive { get; set; }
    public DateTime? TrialEndDate { get; set; }
    public DateTime JoinedDate { get; set; }
    public DateTime LastActivityDate { get; set; }

    // Storage and limits
    public long StorageUsedMB { get; set; }
    public long? MaxStorageMB { get; set; }
    public int? MaxStudents { get; set; }
    public int? MaxFaculty { get; set; }
}

/// <summary>
/// Response model for current tenant information
/// </summary>
public class TenantInfoResponse
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsTrialActive { get; set; }
    public DateTime? TrialEndDate { get; set; }
    public string? CustomDomain { get; set; }
    public string DefaultLanguage { get; set; } = string.Empty;
    public string TimeZone { get; set; } = string.Empty;
    public string Currency { get; set; } = string.Empty;
}

/// <summary>
/// Response model for tenant existence check
/// </summary>
public class TenantExistsResponse
{
    public bool Exists { get; set; }
    public string Identifier { get; set; } = string.Empty;
    public Guid? TenantId { get; set; }
    public string? TenantName { get; set; }
    public string? FoundBy { get; set; }
}

/// <summary>
/// Summary model for tenant listing
/// </summary>
public class TenantSummary
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? CustomDomain { get; set; }
    public DateTime JoinedDate { get; set; }
    public bool IsTrialActive { get; set; }
    public DateTime? TrialEndDate { get; set; }
}
