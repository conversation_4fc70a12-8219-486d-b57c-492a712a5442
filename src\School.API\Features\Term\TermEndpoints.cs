using Carter;
using Microsoft.AspNetCore.Mvc;
using School.API.Common;
using School.Application.DTOs;
using School.Application.Features.Term;

namespace School.API.Features.Term;

public class TermEndpoints : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/terms").WithTags("Terms");

        // Get all terms with filtering and pagination
        group.MapGet("/", async ([AsParameters] TermFilterDto filter, [FromServices] ITermService termService) =>
        {
            var (terms, totalCount) = await termService.GetAllTermsAsync(filter);
            var response = new { TotalCount = totalCount, Items = terms };
            return ApiResults.ApiOk(response, "Terms retrieved successfully");
        }).WithName("GetAllTerms").WithOpenApi();

        // Get term by ID
        group.MapGet("/{id}", async ([FromRoute] Guid id, [FromServices] ITermService termService) =>
        {
            var term = await termService.GetTermByIdAsync(id);
            if (term == null)
            {
                return ApiResults.ApiNotFound("Term not found");
            }
            return ApiResults.ApiOk(term, "Term retrieved successfully");
        }).WithName("GetTermById").WithOpenApi();

        // Get terms by academic year
        group.MapGet("/academic-year/{academicYearId}", async ([FromRoute] Guid academicYearId, [FromServices] ITermService termService) =>
        {
            var terms = await termService.GetTermsByAcademicYearAsync(academicYearId);
            return ApiResults.ApiOk(terms, "Terms retrieved successfully");
        }).WithName("GetTermsByAcademicYear").WithOpenApi();

        // Get current term
        group.MapGet("/current", async ([FromServices] ITermService termService) =>
        {
            var currentTerm = await termService.GetCurrentTermAsync();
            if (currentTerm == null)
            {
                return ApiResults.ApiNotFound("No current term found");
            }
            return ApiResults.ApiOk(currentTerm, "Current term retrieved successfully");
        }).WithName("GetCurrentTerm").WithOpenApi();

        // Get active term by academic year
        group.MapGet("/academic-year/{academicYearId}/active", async ([FromRoute] Guid academicYearId, [FromServices] ITermService termService) =>
        {
            var activeTerm = await termService.GetActiveTermByAcademicYearAsync(academicYearId);
            if (activeTerm == null)
            {
                return ApiResults.ApiNotFound("No active term found for the academic year");
            }
            return ApiResults.ApiOk(activeTerm, "Active term retrieved successfully");
        }).WithName("GetActiveTermByAcademicYear").WithOpenApi();

        // Create new term
        group.MapPost("/", async ([FromBody] CreateTermDto termDto, [FromServices] ITermService termService) =>
        {
            try
            {
                var termId = await termService.CreateTermAsync(termDto);
                return ApiResults.ApiCreated($"/api/terms/{termId}", termId.ToString(), "Term created successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CreateTerm")
        .WithOpenApi();

        // Update term
        group.MapPut("/{id}", async ([FromRoute] Guid id, [FromBody] UpdateTermDto termDto, [FromServices] ITermService termService) =>
        {
            try
            {
                var result = await termService.UpdateTermAsync(id, termDto);
                if (!result)
                {
                    return ApiResults.ApiNotFound("Term not found");
                }
                return ApiResults.ApiOk("Term updated successfully");
            }
            catch (InvalidOperationException ex)
            {
                return ApiResults.ApiBadRequest(ex.Message);
            }
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateTerm")
        .WithOpenApi();

        // Delete term
        group.MapDelete("/{id}", async ([FromRoute] Guid id, [FromServices] ITermService termService) =>
        {
            var result = await termService.DeleteTermAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot delete term. It may have associated data or not exist.");
            }
            return ApiResults.ApiOk("Term deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteTerm")
        .WithOpenApi();

        // Activate term
        group.MapPost("/{id}/activate", async ([FromRoute] Guid id, [FromServices] ITermService termService) =>
        {
            var result = await termService.ActivateTermAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot activate term. It may not exist or not be in planned status.");
            }
            return ApiResults.ApiOk("Term activated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("ActivateTerm")
        .WithOpenApi();

        // Complete term
        group.MapPost("/{id}/complete", async ([FromRoute] Guid id, [FromServices] ITermService termService) =>
        {
            var result = await termService.CompleteTermAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot complete term. It may not exist or not be active.");
            }
            return ApiResults.ApiOk("Term completed successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CompleteTerm")
        .WithOpenApi();

        // Cancel term
        group.MapPost("/{id}/cancel", async ([FromRoute] Guid id, [FromServices] ITermService termService) =>
        {
            var result = await termService.CancelTermAsync(id);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot cancel term. It may not exist.");
            }
            return ApiResults.ApiOk("Term cancelled successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("CancelTerm")
        .WithOpenApi();

        // Reorder terms
        group.MapPost("/academic-year/{academicYearId}/reorder", async ([FromRoute] Guid academicYearId, [FromBody] List<Guid> termIds, [FromServices] ITermService termService) =>
        {
            var result = await termService.ReorderTermsAsync(academicYearId, termIds);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Cannot reorder terms. Invalid term IDs or academic year.");
            }
            return ApiResults.ApiOk("Terms reordered successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("ReorderTerms")
        .WithOpenApi();

        // Get term statistics
        group.MapGet("/{id}/statistics", async ([FromRoute] Guid id, [FromServices] ITermService termService) =>
        {
            var statistics = await termService.GetTermStatisticsAsync(id);
            return ApiResults.ApiOk(statistics, "Term statistics retrieved successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("GetTermStatistics")
        .WithOpenApi();

        // Translation endpoints
        var translationGroup = group.MapGroup("/{termId}/translations");

        // Add translation
        translationGroup.MapPost("/", async ([FromRoute] Guid termId, [FromBody] CreateTermTranslationDto translationDto, [FromServices] ITermService termService) =>
        {
            var result = await termService.AddTranslationAsync(termId, translationDto);
            if (!result)
            {
                return ApiResults.ApiBadRequest("Translation already exists or term not found");
            }
            return ApiResults.ApiOk("Translation added successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("AddTermTranslation")
        .WithOpenApi();

        // Update translation
        translationGroup.MapPut("/{languageCode}", async ([FromRoute] Guid termId, [FromRoute] string languageCode, [FromBody] UpdateTermTranslationDto translationDto, [FromServices] ITermService termService) =>
        {
            var result = await termService.UpdateTranslationAsync(termId, languageCode, translationDto);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation updated successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("UpdateTermTranslation")
        .WithOpenApi();

        // Delete translation
        translationGroup.MapDelete("/{languageCode}", async ([FromRoute] Guid termId, [FromRoute] string languageCode, [FromServices] ITermService termService) =>
        {
            var result = await termService.DeleteTranslationAsync(termId, languageCode);
            if (!result)
            {
                return ApiResults.ApiNotFound("Translation not found");
            }
            return ApiResults.ApiOk("Translation deleted successfully");
        })
        .RequireAuthorization("AdminPolicy")
        .WithName("DeleteTermTranslation")
        .WithOpenApi();

        // Get all translations
        translationGroup.MapGet("/", async ([FromRoute] Guid termId, [FromServices] ITermService termService) =>
        {
            var translations = await termService.GetTranslationsAsync(termId);
            return ApiResults.ApiOk(translations, "Translations retrieved successfully");
        })
        .WithName("GetTermTranslations")
        .WithOpenApi();
    }
}
