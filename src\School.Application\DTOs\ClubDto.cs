using System;
using System.Collections.Generic;

namespace School.Application.DTOs
{
    public class ClubDto
    {
        public Guid Id{ get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ShortDescription { get; set; }
        public string Category { get; set; }
        public string MeetingSchedule { get; set; }
        public string Location { get; set; }
        public string Requirements { get; set; }
        public string JoinProcess { get; set; }
        public string ContactEmail { get; set; }
        public string Website { get; set; }
        public string Instagram { get; set; }
        public string Facebook { get; set; }
        public bool IsFeatured { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public int? ProfileImageId { get; set; }
        public string ProfileImageUrl { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        
        // Related collections
        public ICollection<ClubTranslationDto> Translations { get; set; }
        public ICollection<ClubAdvisorDto> Advisors { get; set; }
        public ICollection<ClubLeaderDto> Leaders { get; set; }
        public ICollection<ClubActivityDto> Activities { get; set; }
        public ICollection<ClubAchievementDto> Achievements { get; set; }
        public ICollection<ClubEventDto> Events { get; set; }
        public ICollection<ClubGalleryItemDto> GalleryItems { get; set; }
    }

    /// <summary>
    /// DTO for club member information
    /// </summary>
    public class ClubMemberDto
    {
        public Guid Id { get; set; }
        public Guid ClubId { get; set; }
        public Guid StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public string Role { get; set; } = "Member";
        public DateTime JoinedDate { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// DTO for adding a club member
    /// </summary>
    public class AddClubMemberDto
    {
        public Guid StudentId { get; set; }
        public string Role { get; set; } = "Member";
    }
}
