using School.Domain.Enums;

namespace School.Application.Features.Subject.DTOs;

/// <summary>
/// Subject data transfer object
/// </summary>
public class SubjectDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public SubjectCategory Category { get; set; }
    public SubjectType Type { get; set; }
    public decimal Credits { get; set; }
    public int WeeklyHours { get; set; }
    public int PracticalHours { get; set; }
    public int TheoryHours { get; set; }
    public decimal MaxMarks { get; set; }
    public decimal PassingMarks { get; set; }
    public bool IsActive { get; set; }
    public bool IsMandatory { get; set; }
    public bool HasPractical { get; set; }
    public int DisplayOrder { get; set; }
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Prerequisites { get; set; } = string.Empty;
    public string LearningOutcomes { get; set; } = string.Empty;
    public string AssessmentMethods { get; set; } = string.Empty;
    public string Textbooks { get; set; } = string.Empty;
    public string References { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Create subject DTO
/// </summary>
public class CreateSubjectDto
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public SubjectCategory Category { get; set; } = SubjectCategory.Core;
    public SubjectType Type { get; set; } = SubjectType.Academic;
    public decimal Credits { get; set; }
    public int WeeklyHours { get; set; }
    public int PracticalHours { get; set; }
    public int TheoryHours { get; set; }
    public decimal MaxMarks { get; set; } = 100;
    public decimal PassingMarks { get; set; } = 40;
    public bool IsActive { get; set; } = true;
    public bool IsMandatory { get; set; } = true;
    public bool HasPractical { get; set; } = false;
    public int DisplayOrder { get; set; }
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Prerequisites { get; set; } = string.Empty;
    public string LearningOutcomes { get; set; } = string.Empty;
    public string AssessmentMethods { get; set; } = string.Empty;
    public string Textbooks { get; set; } = string.Empty;
    public string References { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// Update subject DTO
/// </summary>
public class UpdateSubjectDto
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public SubjectCategory Category { get; set; }
    public SubjectType Type { get; set; }
    public decimal Credits { get; set; }
    public int WeeklyHours { get; set; }
    public int PracticalHours { get; set; }
    public int TheoryHours { get; set; }
    public decimal MaxMarks { get; set; }
    public decimal PassingMarks { get; set; }
    public bool IsActive { get; set; }
    public bool IsMandatory { get; set; }
    public bool HasPractical { get; set; }
    public int DisplayOrder { get; set; }
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public string Prerequisites { get; set; } = string.Empty;
    public string LearningOutcomes { get; set; } = string.Empty;
    public string AssessmentMethods { get; set; } = string.Empty;
    public string Textbooks { get; set; } = string.Empty;
    public string References { get; set; } = string.Empty;
    public string Remarks { get; set; } = string.Empty;
}

/// <summary>
/// Subject filter DTO for search and pagination
/// </summary>
public class SubjectFilterDto
{
    public string? SearchTerm { get; set; }
    public SubjectCategory? Category { get; set; }
    public SubjectType? Type { get; set; }
    public bool? IsActive { get; set; }
    public bool? IsMandatory { get; set; }
    public bool? HasPractical { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "Name";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Grade-Subject mapping DTO
/// </summary>
public class GradeSubjectDto
{
    public Guid Id { get; set; }
    public Guid GradeId { get; set; }
    public Guid SubjectId { get; set; }
    public string GradeName { get; set; } = string.Empty;
    public string SubjectName { get; set; } = string.Empty;
    public string SubjectCode { get; set; } = string.Empty;
    public bool IsOptional { get; set; }
    public int DisplayOrder { get; set; }
    public decimal? CustomMaxMarks { get; set; }
    public decimal? CustomPassingMarks { get; set; }
    public int? CustomWeeklyHours { get; set; }
}

/// <summary>
/// Create Grade-Subject mapping DTO
/// </summary>
public class CreateGradeSubjectDto
{
    public Guid GradeId { get; set; }
    public Guid SubjectId { get; set; }
    public bool IsOptional { get; set; } = false;
    public int DisplayOrder { get; set; }
    public decimal? CustomMaxMarks { get; set; }
    public decimal? CustomPassingMarks { get; set; }
    public int? CustomWeeklyHours { get; set; }
}

/// <summary>
/// Faculty-Subject assignment DTO
/// </summary>
public class FacultySubjectDto
{
    public Guid Id { get; set; }
    public Guid FacultyId { get; set; }
    public Guid SubjectId { get; set; }
    public Guid? GradeId { get; set; }
    public Guid? SectionId { get; set; }
    public Guid AcademicYearId { get; set; }
    public string FacultyName { get; set; } = string.Empty;
    public string SubjectName { get; set; } = string.Empty;
    public string SubjectCode { get; set; } = string.Empty;
    public string? GradeName { get; set; }
    public string? SectionName { get; set; }
    public string AcademicYearName { get; set; } = string.Empty;
    public bool IsPrimary { get; set; }
    public DateTime AssignedDate { get; set; }
    public DateTime? UnassignedDate { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Create Faculty-Subject assignment DTO
/// </summary>
public class CreateFacultySubjectDto
{
    public Guid FacultyId { get; set; }
    public Guid SubjectId { get; set; }
    public Guid? GradeId { get; set; }
    public Guid? SectionId { get; set; }
    public Guid AcademicYearId { get; set; }
    public bool IsPrimary { get; set; } = true;
    public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Subject summary DTO for dashboard and reports
/// </summary>
public class SubjectSummaryDto
{
    public int TotalSubjects { get; set; }
    public int CoreSubjects { get; set; }
    public int ElectiveSubjects { get; set; }
    public int OptionalSubjects { get; set; }
    public int AcademicSubjects { get; set; }
    public int VocationalSubjects { get; set; }
    public int ExtracurricularSubjects { get; set; }
    public int ActiveSubjects { get; set; }
    public int InactiveSubjects { get; set; }
    public decimal AverageCredits { get; set; }
    public decimal TotalCredits { get; set; }
}
