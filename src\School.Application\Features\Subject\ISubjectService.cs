using School.Application.Features.Subject.DTOs;

namespace School.Application.Features.Subject;

/// <summary>
/// Subject service interface for managing subjects
/// </summary>
public interface ISubjectService
{
    // Subject CRUD operations
    Task<(IEnumerable<SubjectDto> Subjects, int TotalCount)> GetAllSubjectsAsync(SubjectFilterDto filter);
    Task<SubjectDto?> GetSubjectByIdAsync(Guid id);
    Task<SubjectDto?> GetSubjectByCodeAsync(string code);
    Task<Guid> CreateSubjectAsync(CreateSubjectDto subjectDto);
    Task<bool> UpdateSubjectAsync(Guid id, UpdateSubjectDto subjectDto);
    Task<bool> DeleteSubjectAsync(Guid id);
    Task<bool> ActivateSubjectAsync(Guid id);
    Task<bool> DeactivateSubjectAsync(Guid id);

    // Subject validation
    Task<bool> IsSubjectCodeUniqueAsync(string code, Guid? excludeId = null);
    Task<bool> IsSubjectNameUniqueAsync(string name, Guid? excludeId = null);
    Task<bool> CanDeleteSubjectAsync(Guid id);

    // Grade-Subject management
    Task<IEnumerable<GradeSubjectDto>> GetGradeSubjectsAsync(Guid gradeId);
    Task<IEnumerable<SubjectDto>> GetSubjectsByGradeAsync(Guid gradeId);
    Task<bool> AssignSubjectToGradeAsync(CreateGradeSubjectDto gradeSubjectDto);
    Task<bool> UnassignSubjectFromGradeAsync(Guid gradeId, Guid subjectId);
    Task<bool> UpdateGradeSubjectAsync(Guid gradeId, Guid subjectId, CreateGradeSubjectDto gradeSubjectDto);

    // Faculty-Subject management
    Task<IEnumerable<FacultySubjectDto>> GetFacultySubjectsAsync(Guid facultyId, Guid? academicYearId = null);
    Task<IEnumerable<FacultySubjectDto>> GetSubjectFacultiesAsync(Guid subjectId, Guid? academicYearId = null);
    Task<bool> AssignFacultyToSubjectAsync(CreateFacultySubjectDto facultySubjectDto);
    Task<bool> UnassignFacultyFromSubjectAsync(Guid facultyId, Guid subjectId, Guid academicYearId);
    Task<bool> UpdateFacultySubjectAsync(Guid id, CreateFacultySubjectDto facultySubjectDto);

    // Subject analytics and reporting
    Task<SubjectSummaryDto> GetSubjectSummaryAsync();
    Task<IEnumerable<SubjectDto>> GetSubjectsByTypeAsync(Domain.Enums.SubjectType type);
    Task<IEnumerable<SubjectDto>> GetSubjectsByCategoryAsync(Domain.Enums.SubjectCategory category);
    Task<IEnumerable<SubjectDto>> GetMandatorySubjectsAsync();
    Task<IEnumerable<SubjectDto>> GetOptionalSubjectsAsync();
    Task<IEnumerable<SubjectDto>> GetPracticalSubjectsAsync();

    // Subject search and filtering
    Task<IEnumerable<SubjectDto>> SearchSubjectsAsync(string searchTerm);
    Task<IEnumerable<SubjectDto>> GetSubjectsWithPrerequisitesAsync();
    Task<IEnumerable<SubjectDto>> GetSubjectsByCreditsRangeAsync(decimal minCredits, decimal maxCredits);
    Task<IEnumerable<SubjectDto>> GetSubjectsByHoursRangeAsync(int minHours, int maxHours);

    // Bulk operations
    Task<bool> BulkCreateSubjectsAsync(IEnumerable<CreateSubjectDto> subjects);
    Task<bool> BulkUpdateSubjectsAsync(IEnumerable<(Guid Id, UpdateSubjectDto Subject)> subjects);
    Task<bool> BulkDeleteSubjectsAsync(IEnumerable<Guid> subjectIds);
    Task<bool> BulkActivateSubjectsAsync(IEnumerable<Guid> subjectIds);
    Task<bool> BulkDeactivateSubjectsAsync(IEnumerable<Guid> subjectIds);

    // Import/Export operations
    Task<bool> ImportSubjectsFromCsvAsync(Stream csvStream);
    Task<Stream> ExportSubjectsToCsvAsync(SubjectFilterDto? filter = null);
    Task<Stream> ExportSubjectsToExcelAsync(SubjectFilterDto? filter = null);

    // Subject prerequisites management
    Task<IEnumerable<SubjectDto>> GetSubjectPrerequisitesAsync(Guid subjectId);
    Task<IEnumerable<SubjectDto>> GetSubjectDependentsAsync(Guid subjectId);
    Task<bool> AddSubjectPrerequisiteAsync(Guid subjectId, Guid prerequisiteSubjectId);
    Task<bool> RemoveSubjectPrerequisiteAsync(Guid subjectId, Guid prerequisiteSubjectId);
    Task<bool> ValidateSubjectPrerequisitesAsync(Guid subjectId, IEnumerable<Guid> prerequisiteIds);

    // Subject learning outcomes management
    Task<bool> UpdateSubjectLearningOutcomesAsync(Guid subjectId, string learningOutcomes);
    Task<bool> UpdateSubjectAssessmentMethodsAsync(Guid subjectId, string assessmentMethods);
    Task<bool> UpdateSubjectTextbooksAsync(Guid subjectId, string textbooks);
    Task<bool> UpdateSubjectReferencesAsync(Guid subjectId, string references);

    // Subject statistics
    Task<Dictionary<string, int>> GetSubjectStatisticsByTypeAsync();
    Task<Dictionary<string, int>> GetSubjectStatisticsByCategoryAsync();
    Task<Dictionary<string, decimal>> GetSubjectCreditDistributionAsync();
    Task<Dictionary<string, int>> GetSubjectHourDistributionAsync();
}
