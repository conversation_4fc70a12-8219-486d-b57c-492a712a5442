using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.Features.Subject;
using School.Application.Features.Subject.DTOs;
using School.Domain.Enums;
using System.Globalization;
using System.Text;
using System.Text.Json;

namespace School.Infrastructure.Services;

/// <summary>
/// Analytics and reporting methods for SubjectService
/// </summary>
public partial class SubjectService
{
    #region Subject Analytics and Reporting

    public async Task<SubjectSummaryDto> GetSubjectSummaryAsync()
    {
        try
        {
            var subjects = await _context.Subjects.ToListAsync();

            var summary = new SubjectSummaryDto
            {
                TotalSubjects = subjects.Count,
                CoreSubjects = subjects.Count(s => s.Category == SubjectCategory.Core),
                ElectiveSubjects = subjects.Count(s => s.Category == SubjectCategory.Elective),
                OptionalSubjects = subjects.Count(s => s.Category == SubjectCategory.Optional),
                AcademicSubjects = subjects.Count(s => s.Type == SubjectType.Academic),
                VocationalSubjects = subjects.Count(s => s.Type == SubjectType.Vocational),
                ExtracurricularSubjects = subjects.Count(s => s.Type == SubjectType.Extracurricular),
                ActiveSubjects = subjects.Count(s => s.IsActive),
                InactiveSubjects = subjects.Count(s => !s.IsActive),
                AverageCredits = subjects.Any() ? subjects.Average(s => s.Credits) : 0,
                TotalCredits = subjects.Sum(s => s.Credits)
            };

            return summary;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating subject summary");
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetSubjectsByTypeAsync(SubjectType type)
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => s.Type == type && s.IsActive)
                .OrderBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subjects by type: {Type}", type);
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetSubjectsByCategoryAsync(SubjectCategory category)
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => s.Category == category && s.IsActive)
                .OrderBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subjects by category: {Category}", category);
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetMandatorySubjectsAsync()
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => s.IsMandatory && s.IsActive)
                .OrderBy(s => s.DisplayOrder)
                .ThenBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving mandatory subjects");
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetOptionalSubjectsAsync()
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => !s.IsMandatory && s.IsActive)
                .OrderBy(s => s.DisplayOrder)
                .ThenBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving optional subjects");
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetPracticalSubjectsAsync()
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => s.HasPractical && s.IsActive)
                .OrderBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving practical subjects");
            throw;
        }
    }

    #endregion

    #region Subject Search and Filtering

    public async Task<IEnumerable<SubjectDto>> SearchSubjectsAsync(string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<SubjectDto>();
            }

            var term = searchTerm.ToLower().Trim();

            var subjects = await _context.Subjects
                .Where(s => s.IsActive && (
                    s.Name.ToLower().Contains(term) ||
                    s.Code.ToLower().Contains(term) ||
                    s.Description.ToLower().Contains(term)))
                .OrderBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching subjects: {SearchTerm}", searchTerm);
            throw;
        }
    }

    #endregion
}
