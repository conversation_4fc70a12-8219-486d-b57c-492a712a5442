using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.Features.Subject;
using School.Application.Features.Subject.DTOs;
using School.Domain.Enums;
using System.Globalization;
using System.Text;
using System.Text.Json;

namespace School.Infrastructure.Services;

/// <summary>
/// Extended methods for SubjectService (bulk operations, import/export, etc.)
/// </summary>
public partial class SubjectService
{
    #region Additional Search and Filtering Methods

    public async Task<IEnumerable<SubjectDto>> GetSubjectsWithPrerequisitesAsync()
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => s.IsActive && !string.IsNullOrEmpty(s.Prerequisites))
                .OrderBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subjects with prerequisites");
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetSubjectsByCreditsRangeAsync(decimal minCredits, decimal maxCredits)
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => s.IsActive && s.Credits >= minCredits && s.Credits <= maxCredits)
                .OrderBy(s => s.Credits)
                .ThenBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subjects by credits range: {MinCredits}-{MaxCredits}", minCredits, maxCredits);
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetSubjectsByHoursRangeAsync(int minHours, int maxHours)
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => s.IsActive && s.WeeklyHours >= minHours && s.WeeklyHours <= maxHours)
                .OrderBy(s => s.WeeklyHours)
                .ThenBy(s => s.Name)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subjects by hours range: {MinHours}-{MaxHours}", minHours, maxHours);
            throw;
        }
    }

    #endregion

    #region Bulk Operations

    public async Task<bool> BulkCreateSubjectsAsync(IEnumerable<CreateSubjectDto> subjects)
    {
        try
        {
            var tenantId = _tenantService.GetCurrentTenantId() ?? throw new InvalidOperationException("No tenant context available.");
            var subjectEntities = new List<Domain.Entities.Subject>();

            foreach (var subjectDto in subjects)
            {
                // Validate unique constraints
                if (!await IsSubjectCodeUniqueAsync(subjectDto.Code))
                {
                    throw new InvalidOperationException($"Subject code '{subjectDto.Code}' already exists.");
                }

                if (!await IsSubjectNameUniqueAsync(subjectDto.Name))
                {
                    throw new InvalidOperationException($"Subject name '{subjectDto.Name}' already exists.");
                }

                var subject = new Domain.Entities.Subject
                {
                    TenantId = tenantId,
                    Name = subjectDto.Name.Trim(),
                    Code = subjectDto.Code.Trim().ToUpper(),
                    Description = subjectDto.Description?.Trim() ?? string.Empty,
                    Category = subjectDto.Category,
                    Type = subjectDto.Type,
                    Credits = subjectDto.Credits,
                    WeeklyHours = subjectDto.WeeklyHours,
                    PracticalHours = subjectDto.PracticalHours,
                    TheoryHours = subjectDto.TheoryHours,
                    MaxMarks = subjectDto.MaxMarks,
                    PassingMarks = subjectDto.PassingMarks,
                    IsActive = subjectDto.IsActive,
                    IsMandatory = subjectDto.IsMandatory,
                    HasPractical = subjectDto.HasPractical,
                    DisplayOrder = subjectDto.DisplayOrder,
                    Icon = subjectDto.Icon?.Trim() ?? string.Empty,
                    Color = subjectDto.Color?.Trim() ?? string.Empty,
                    Prerequisites = subjectDto.Prerequisites?.Trim() ?? string.Empty,
                    LearningOutcomes = subjectDto.LearningOutcomes?.Trim() ?? string.Empty,
                    AssessmentMethods = subjectDto.AssessmentMethods?.Trim() ?? string.Empty,
                    Textbooks = subjectDto.Textbooks?.Trim() ?? string.Empty,
                    References = subjectDto.References?.Trim() ?? string.Empty,
                    Remarks = subjectDto.Remarks?.Trim() ?? string.Empty
                };

                subjectEntities.Add(subject);
            }

            _context.Subjects.AddRange(subjectEntities);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Bulk created {Count} subjects successfully", subjectEntities.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk creating subjects");
            throw;
        }
    }

    public async Task<bool> BulkUpdateSubjectsAsync(IEnumerable<(Guid Id, UpdateSubjectDto Subject)> subjects)
    {
        try
        {
            var subjectIds = subjects.Select(s => s.Id).ToList();
            var existingSubjects = await _context.Subjects
                .Where(s => subjectIds.Contains(s.Id))
                .ToListAsync();

            foreach (var (id, subjectDto) in subjects)
            {
                var subject = existingSubjects.FirstOrDefault(s => s.Id == id);
                if (subject == null)
                {
                    _logger.LogWarning("Subject not found for bulk update: {SubjectId}", id);
                    continue;
                }

                // Validate unique constraints (excluding current subject)
                if (!await IsSubjectCodeUniqueAsync(subjectDto.Code, id))
                {
                    throw new InvalidOperationException($"Subject code '{subjectDto.Code}' already exists.");
                }

                if (!await IsSubjectNameUniqueAsync(subjectDto.Name, id))
                {
                    throw new InvalidOperationException($"Subject name '{subjectDto.Name}' already exists.");
                }

                // Update properties
                subject.Name = subjectDto.Name.Trim();
                subject.Code = subjectDto.Code.Trim().ToUpper();
                subject.Description = subjectDto.Description?.Trim() ?? string.Empty;
                subject.Category = subjectDto.Category;
                subject.Type = subjectDto.Type;
                subject.Credits = subjectDto.Credits;
                subject.WeeklyHours = subjectDto.WeeklyHours;
                subject.PracticalHours = subjectDto.PracticalHours;
                subject.TheoryHours = subjectDto.TheoryHours;
                subject.MaxMarks = subjectDto.MaxMarks;
                subject.PassingMarks = subjectDto.PassingMarks;
                subject.IsActive = subjectDto.IsActive;
                subject.IsMandatory = subjectDto.IsMandatory;
                subject.HasPractical = subjectDto.HasPractical;
                subject.DisplayOrder = subjectDto.DisplayOrder;
                subject.Icon = subjectDto.Icon?.Trim() ?? string.Empty;
                subject.Color = subjectDto.Color?.Trim() ?? string.Empty;
                subject.Prerequisites = subjectDto.Prerequisites?.Trim() ?? string.Empty;
                subject.LearningOutcomes = subjectDto.LearningOutcomes?.Trim() ?? string.Empty;
                subject.AssessmentMethods = subjectDto.AssessmentMethods?.Trim() ?? string.Empty;
                subject.Textbooks = subjectDto.Textbooks?.Trim() ?? string.Empty;
                subject.References = subjectDto.References?.Trim() ?? string.Empty;
                subject.Remarks = subjectDto.Remarks?.Trim() ?? string.Empty;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Bulk updated {Count} subjects successfully", existingSubjects.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating subjects");
            throw;
        }
    }

    public async Task<bool> BulkDeleteSubjectsAsync(IEnumerable<Guid> subjectIds)
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => subjectIds.Contains(s.Id))
                .ToListAsync();

            foreach (var subject in subjects)
            {
                if (!await CanDeleteSubjectAsync(subject.Id))
                {
                    throw new InvalidOperationException($"Cannot delete subject '{subject.Name}' as it has dependencies.");
                }
            }

            _context.Subjects.RemoveRange(subjects);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Bulk deleted {Count} subjects successfully", subjects.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk deleting subjects");
            throw;
        }
    }

    public async Task<bool> BulkActivateSubjectsAsync(IEnumerable<Guid> subjectIds)
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => subjectIds.Contains(s.Id))
                .ToListAsync();

            foreach (var subject in subjects)
            {
                subject.IsActive = true;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Bulk activated {Count} subjects successfully", subjects.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk activating subjects");
            throw;
        }
    }

    public async Task<bool> BulkDeactivateSubjectsAsync(IEnumerable<Guid> subjectIds)
    {
        try
        {
            var subjects = await _context.Subjects
                .Where(s => subjectIds.Contains(s.Id))
                .ToListAsync();

            foreach (var subject in subjects)
            {
                subject.IsActive = false;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Bulk deactivated {Count} subjects successfully", subjects.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk deactivating subjects");
            throw;
        }
    }

    #endregion
}
