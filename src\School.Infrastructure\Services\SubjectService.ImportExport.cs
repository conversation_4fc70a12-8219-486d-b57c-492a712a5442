using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.Features.Subject;
using School.Application.Features.Subject.DTOs;
using School.Domain.Enums;
using System.Globalization;
using System.Text;
using System.Text.Json;

namespace School.Infrastructure.Services;

/// <summary>
/// Import/Export and remaining methods for SubjectService
/// </summary>
public partial class SubjectService
{
    #region Import/Export Operations

    public async Task<bool> ImportSubjectsFromCsvAsync(Stream csvStream)
    {
        try
        {
            using var reader = new StreamReader(csvStream);
            var subjects = new List<CreateSubjectDto>();
            
            // Skip header line
            await reader.ReadLineAsync();
            
            string? line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                var values = line.Split(',');
                if (values.Length < 8) continue; // Minimum required fields
                
                var subject = new CreateSubjectDto
                {
                    Name = values[0].Trim('"'),
                    Code = values[1].Trim('"'),
                    Description = values[2].Trim('"'),
                    Category = Enum.TryParse<SubjectCategory>(values[3].Trim('"'), out var category) ? category : SubjectCategory.Core,
                    Type = Enum.TryParse<SubjectType>(values[4].Trim('"'), out var type) ? type : SubjectType.Academic,
                    Credits = decimal.TryParse(values[5].Trim('"'), out var credits) ? credits : 0,
                    WeeklyHours = int.TryParse(values[6].Trim('"'), out var hours) ? hours : 0,
                    MaxMarks = decimal.TryParse(values[7].Trim('"'), out var maxMarks) ? maxMarks : 100,
                    PassingMarks = values.Length > 8 && decimal.TryParse(values[8].Trim('"'), out var passingMarks) ? passingMarks : 40,
                    IsMandatory = values.Length > 9 && bool.TryParse(values[9].Trim('"'), out var mandatory) ? mandatory : true,
                    HasPractical = values.Length > 10 && bool.TryParse(values[10].Trim('"'), out var practical) ? practical : false,
                    IsActive = values.Length > 11 && bool.TryParse(values[11].Trim('"'), out var active) ? active : true
                };
                
                subjects.Add(subject);
            }
            
            return await BulkCreateSubjectsAsync(subjects);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing subjects from CSV");
            throw;
        }
    }

    public async Task<Stream> ExportSubjectsToCsvAsync(SubjectFilterDto? filter = null)
    {
        try
        {
            filter ??= new SubjectFilterDto { Page = 1, PageSize = int.MaxValue };
            
            var (subjects, _) = await GetAllSubjectsAsync(filter);
            
            var csv = new StringBuilder();
            csv.AppendLine("Name,Code,Description,Category,Type,Credits,WeeklyHours,MaxMarks,PassingMarks,IsMandatory,HasPractical,IsActive");
            
            foreach (var subject in subjects)
            {
                csv.AppendLine($"\"{subject.Name}\",\"{subject.Code}\",\"{subject.Description}\",\"{subject.Category}\",\"{subject.Type}\",{subject.Credits},{subject.WeeklyHours},{subject.MaxMarks},{subject.PassingMarks},{subject.IsMandatory},{subject.HasPractical},{subject.IsActive}");
            }
            
            var stream = new MemoryStream(Encoding.UTF8.GetBytes(csv.ToString()));
            return stream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting subjects to CSV");
            throw;
        }
    }

    public async Task<Stream> ExportSubjectsToExcelAsync(SubjectFilterDto? filter = null)
    {
        try
        {
            // For now, return CSV format. In a real implementation, you would use a library like EPPlus or ClosedXML
            return await ExportSubjectsToCsvAsync(filter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting subjects to Excel");
            throw;
        }
    }

    #endregion

    #region Subject Prerequisites Management

    public async Task<IEnumerable<SubjectDto>> GetSubjectPrerequisitesAsync(Guid subjectId)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(subjectId);
            if (subject == null || string.IsNullOrEmpty(subject.Prerequisites))
            {
                return new List<SubjectDto>();
            }

            var prerequisiteIds = JsonSerializer.Deserialize<List<Guid>>(subject.Prerequisites) ?? new List<Guid>();
            
            var prerequisites = await _context.Subjects
                .Where(s => prerequisiteIds.Contains(s.Id) && s.IsActive)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return prerequisites;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject prerequisites: {SubjectId}", subjectId);
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetSubjectDependentsAsync(Guid subjectId)
    {
        try
        {
            var dependents = await _context.Subjects
                .Where(s => s.IsActive && !string.IsNullOrEmpty(s.Prerequisites))
                .ToListAsync();

            var result = new List<SubjectDto>();

            foreach (var subject in dependents)
            {
                try
                {
                    var prerequisiteIds = JsonSerializer.Deserialize<List<Guid>>(subject.Prerequisites) ?? new List<Guid>();
                    if (prerequisiteIds.Contains(subjectId))
                    {
                        result.Add(new SubjectDto
                        {
                            Id = subject.Id,
                            Name = subject.Name,
                            Code = subject.Code,
                            Description = subject.Description,
                            Category = subject.Category,
                            Type = subject.Type,
                            Credits = subject.Credits,
                            WeeklyHours = subject.WeeklyHours,
                            PracticalHours = subject.PracticalHours,
                            TheoryHours = subject.TheoryHours,
                            MaxMarks = subject.MaxMarks,
                            PassingMarks = subject.PassingMarks,
                            IsActive = subject.IsActive,
                            IsMandatory = subject.IsMandatory,
                            HasPractical = subject.HasPractical,
                            DisplayOrder = subject.DisplayOrder,
                            Icon = subject.Icon,
                            Color = subject.Color,
                            Prerequisites = subject.Prerequisites,
                            LearningOutcomes = subject.LearningOutcomes,
                            AssessmentMethods = subject.AssessmentMethods,
                            Textbooks = subject.Textbooks,
                            References = subject.References,
                            Remarks = subject.Remarks,
                            CreatedAt = subject.CreatedAt,
                            UpdatedAt = subject.LastModifiedAt
                        });
                    }
                }
                catch (JsonException)
                {
                    // Skip subjects with invalid JSON in prerequisites
                    continue;
                }
            }

            return result.OrderBy(s => s.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject dependents: {SubjectId}", subjectId);
            throw;
        }
    }

    public async Task<bool> AddSubjectPrerequisiteAsync(Guid subjectId, Guid prerequisiteSubjectId)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(subjectId);
            if (subject == null)
            {
                return false;
            }

            var prerequisiteIds = string.IsNullOrEmpty(subject.Prerequisites) 
                ? new List<Guid>() 
                : JsonSerializer.Deserialize<List<Guid>>(subject.Prerequisites) ?? new List<Guid>();

            if (!prerequisiteIds.Contains(prerequisiteSubjectId))
            {
                prerequisiteIds.Add(prerequisiteSubjectId);
                subject.Prerequisites = JsonSerializer.Serialize(prerequisiteIds);
                await _context.SaveChangesAsync();
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding subject prerequisite: {SubjectId}, {PrerequisiteId}", subjectId, prerequisiteSubjectId);
            throw;
        }
    }

    public async Task<bool> RemoveSubjectPrerequisiteAsync(Guid subjectId, Guid prerequisiteSubjectId)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(subjectId);
            if (subject == null || string.IsNullOrEmpty(subject.Prerequisites))
            {
                return false;
            }

            var prerequisiteIds = JsonSerializer.Deserialize<List<Guid>>(subject.Prerequisites) ?? new List<Guid>();
            
            if (prerequisiteIds.Remove(prerequisiteSubjectId))
            {
                subject.Prerequisites = prerequisiteIds.Any() ? JsonSerializer.Serialize(prerequisiteIds) : string.Empty;
                await _context.SaveChangesAsync();
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing subject prerequisite: {SubjectId}, {PrerequisiteId}", subjectId, prerequisiteSubjectId);
            throw;
        }
    }

    public async Task<bool> ValidateSubjectPrerequisitesAsync(Guid subjectId, IEnumerable<Guid> prerequisiteIds)
    {
        try
        {
            // Check for circular dependencies
            var visited = new HashSet<Guid>();
            var recursionStack = new HashSet<Guid>();

            bool HasCircularDependency(Guid currentSubjectId)
            {
                if (recursionStack.Contains(currentSubjectId))
                {
                    return true;
                }

                if (visited.Contains(currentSubjectId))
                {
                    return false;
                }

                visited.Add(currentSubjectId);
                recursionStack.Add(currentSubjectId);

                var subject = _context.Subjects.Find(currentSubjectId);
                if (subject != null && !string.IsNullOrEmpty(subject.Prerequisites))
                {
                    try
                    {
                        var deps = JsonSerializer.Deserialize<List<Guid>>(subject.Prerequisites) ?? new List<Guid>();
                        foreach (var dep in deps)
                        {
                            if (HasCircularDependency(dep))
                            {
                                return true;
                            }
                        }
                    }
                    catch (JsonException)
                    {
                        // Skip invalid JSON
                    }
                }

                recursionStack.Remove(currentSubjectId);
                return false;
            }

            // Check if adding these prerequisites would create circular dependencies
            foreach (var prerequisiteId in prerequisiteIds)
            {
                if (prerequisiteId == subjectId || HasCircularDependency(prerequisiteId))
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating subject prerequisites: {SubjectId}", subjectId);
            throw;
        }
    }

    #endregion

    #region Subject Learning Outcomes Management

    public async Task<bool> UpdateSubjectLearningOutcomesAsync(Guid subjectId, string learningOutcomes)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(subjectId);
            if (subject == null)
            {
                return false;
            }

            subject.LearningOutcomes = learningOutcomes?.Trim() ?? string.Empty;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subject learning outcomes: {SubjectId}", subjectId);
            throw;
        }
    }

    public async Task<bool> UpdateSubjectAssessmentMethodsAsync(Guid subjectId, string assessmentMethods)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(subjectId);
            if (subject == null)
            {
                return false;
            }

            subject.AssessmentMethods = assessmentMethods?.Trim() ?? string.Empty;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subject assessment methods: {SubjectId}", subjectId);
            throw;
        }
    }

    public async Task<bool> UpdateSubjectTextbooksAsync(Guid subjectId, string textbooks)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(subjectId);
            if (subject == null)
            {
                return false;
            }

            subject.Textbooks = textbooks?.Trim() ?? string.Empty;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subject textbooks: {SubjectId}", subjectId);
            throw;
        }
    }

    public async Task<bool> UpdateSubjectReferencesAsync(Guid subjectId, string references)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(subjectId);
            if (subject == null)
            {
                return false;
            }

            subject.References = references?.Trim() ?? string.Empty;
            await _context.SaveChangesAsync();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subject references: {SubjectId}", subjectId);
            throw;
        }
    }

    #endregion

    #region Subject Statistics

    public async Task<Dictionary<string, int>> GetSubjectStatisticsByTypeAsync()
    {
        try
        {
            var statistics = await _context.Subjects
                .Where(s => s.IsActive)
                .GroupBy(s => s.Type)
                .Select(g => new { Type = g.Key.ToString(), Count = g.Count() })
                .ToDictionaryAsync(x => x.Type, x => x.Count);

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject statistics by type");
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetSubjectStatisticsByCategoryAsync()
    {
        try
        {
            var statistics = await _context.Subjects
                .Where(s => s.IsActive)
                .GroupBy(s => s.Category)
                .Select(g => new { Category = g.Key.ToString(), Count = g.Count() })
                .ToDictionaryAsync(x => x.Category, x => x.Count);

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject statistics by category");
            throw;
        }
    }

    public async Task<Dictionary<string, decimal>> GetSubjectCreditDistributionAsync()
    {
        try
        {
            var distribution = await _context.Subjects
                .Where(s => s.IsActive)
                .GroupBy(s => s.Credits)
                .Select(g => new { Credits = g.Key.ToString(), Count = g.Count() })
                .ToDictionaryAsync(x => x.Credits, x => (decimal)x.Count);

            return distribution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject credit distribution");
            throw;
        }
    }

    public async Task<Dictionary<string, int>> GetSubjectHourDistributionAsync()
    {
        try
        {
            var distribution = await _context.Subjects
                .Where(s => s.IsActive)
                .GroupBy(s => s.WeeklyHours)
                .Select(g => new { Hours = g.Key.ToString(), Count = g.Count() })
                .ToDictionaryAsync(x => x.Hours, x => x.Count);

            return distribution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject hour distribution");
            throw;
        }
    }

    #endregion
}
