using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using School.Application.Common.Interfaces;
using School.Application.Features.Subject;
using School.Application.Features.Subject.DTOs;
using School.Domain.Entities;
using School.Domain.Enums;
using System.Globalization;
using System.Text;
using System.Text.Json;

namespace School.Infrastructure.Services;

/// <summary>
/// Complete implementation of Subject service with all functionality
/// </summary>
public partial class SubjectService : ISubjectService
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<SubjectService> _logger;
    private readonly ITenantService _tenantService;

    public SubjectService(
        IApplicationDbContext context,
        ILogger<SubjectService> logger,
        ITenantService tenantService)
    {
        _context = context;
        _logger = logger;
        _tenantService = tenantService;
    }

    #region Subject CRUD Operations

    public async Task<(IEnumerable<SubjectDto> Subjects, int TotalCount)> GetAllSubjectsAsync(SubjectFilterDto filter)
    {
        try
        {
            var query = _context.Subjects.AsQueryable();

            // Apply filters
            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.ToLower();
                query = query.Where(s => s.Name.ToLower().Contains(searchTerm) ||
                                        s.Code.ToLower().Contains(searchTerm) ||
                                        s.Description.ToLower().Contains(searchTerm));
            }

            if (filter.Category.HasValue)
            {
                query = query.Where(s => s.Category == filter.Category.Value);
            }

            if (filter.Type.HasValue)
            {
                query = query.Where(s => s.Type == filter.Type.Value);
            }

            if (filter.IsActive.HasValue)
            {
                query = query.Where(s => s.IsActive == filter.IsActive.Value);
            }

            if (filter.IsMandatory.HasValue)
            {
                query = query.Where(s => s.IsMandatory == filter.IsMandatory.Value);
            }

            if (filter.HasPractical.HasValue)
            {
                query = query.Where(s => s.HasPractical == filter.HasPractical.Value);
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply sorting
            query = filter.SortBy.ToLower() switch
            {
                "code" => filter.SortDescending ? query.OrderByDescending(s => s.Code) : query.OrderBy(s => s.Code),
                "category" => filter.SortDescending ? query.OrderByDescending(s => s.Category) : query.OrderBy(s => s.Category),
                "type" => filter.SortDescending ? query.OrderByDescending(s => s.Type) : query.OrderBy(s => s.Type),
                "credits" => filter.SortDescending ? query.OrderByDescending(s => s.Credits) : query.OrderBy(s => s.Credits),
                "weeklyhours" => filter.SortDescending ? query.OrderByDescending(s => s.WeeklyHours) : query.OrderBy(s => s.WeeklyHours),
                "displayorder" => filter.SortDescending ? query.OrderByDescending(s => s.DisplayOrder) : query.OrderBy(s => s.DisplayOrder),
                "createdat" => filter.SortDescending ? query.OrderByDescending(s => s.CreatedAt) : query.OrderBy(s => s.CreatedAt),
                _ => filter.SortDescending ? query.OrderByDescending(s => s.Name) : query.OrderBy(s => s.Name)
            };

            // Apply pagination
            var subjects = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .ToListAsync();

            return (subjects, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subjects with filter: {@Filter}", filter);
            throw;
        }
    }

    public async Task<SubjectDto?> GetSubjectByIdAsync(Guid id)
    {
        try
        {
            var subject = await _context.Subjects
                .Where(s => s.Id == id)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .FirstOrDefaultAsync();

            return subject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject by ID: {SubjectId}", id);
            throw;
        }
    }

    public async Task<SubjectDto?> GetSubjectByCodeAsync(string code)
    {
        try
        {
            var subject = await _context.Subjects
                .Where(s => s.Code == code)
                .Select(s => new SubjectDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Code = s.Code,
                    Description = s.Description,
                    Category = s.Category,
                    Type = s.Type,
                    Credits = s.Credits,
                    WeeklyHours = s.WeeklyHours,
                    PracticalHours = s.PracticalHours,
                    TheoryHours = s.TheoryHours,
                    MaxMarks = s.MaxMarks,
                    PassingMarks = s.PassingMarks,
                    IsActive = s.IsActive,
                    IsMandatory = s.IsMandatory,
                    HasPractical = s.HasPractical,
                    DisplayOrder = s.DisplayOrder,
                    Icon = s.Icon,
                    Color = s.Color,
                    Prerequisites = s.Prerequisites,
                    LearningOutcomes = s.LearningOutcomes,
                    AssessmentMethods = s.AssessmentMethods,
                    Textbooks = s.Textbooks,
                    References = s.References,
                    Remarks = s.Remarks,
                    CreatedAt = s.CreatedAt,
                    UpdatedAt = s.LastModifiedAt
                })
                .FirstOrDefaultAsync();

            return subject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject by code: {SubjectCode}", code);
            throw;
        }
    }

    public async Task<Guid> CreateSubjectAsync(CreateSubjectDto subjectDto)
    {
        try
        {
            // Validate unique constraints
            if (!await IsSubjectCodeUniqueAsync(subjectDto.Code))
            {
                throw new InvalidOperationException($"Subject code '{subjectDto.Code}' already exists.");
            }

            if (!await IsSubjectNameUniqueAsync(subjectDto.Name))
            {
                throw new InvalidOperationException($"Subject name '{subjectDto.Name}' already exists.");
            }

            var tenantId = _tenantService.GetCurrentTenantId() ?? throw new InvalidOperationException("No tenant context available.");

            var subject = new Domain.Entities.Subject
            {
                TenantId = tenantId,
                Name = subjectDto.Name.Trim(),
                Code = subjectDto.Code.Trim().ToUpper(),
                Description = subjectDto.Description?.Trim() ?? string.Empty,
                Category = subjectDto.Category,
                Type = subjectDto.Type,
                Credits = subjectDto.Credits,
                WeeklyHours = subjectDto.WeeklyHours,
                PracticalHours = subjectDto.PracticalHours,
                TheoryHours = subjectDto.TheoryHours,
                MaxMarks = subjectDto.MaxMarks,
                PassingMarks = subjectDto.PassingMarks,
                IsActive = subjectDto.IsActive,
                IsMandatory = subjectDto.IsMandatory,
                HasPractical = subjectDto.HasPractical,
                DisplayOrder = subjectDto.DisplayOrder,
                Icon = subjectDto.Icon?.Trim() ?? string.Empty,
                Color = subjectDto.Color?.Trim() ?? string.Empty,
                Prerequisites = subjectDto.Prerequisites?.Trim() ?? string.Empty,
                LearningOutcomes = subjectDto.LearningOutcomes?.Trim() ?? string.Empty,
                AssessmentMethods = subjectDto.AssessmentMethods?.Trim() ?? string.Empty,
                Textbooks = subjectDto.Textbooks?.Trim() ?? string.Empty,
                References = subjectDto.References?.Trim() ?? string.Empty,
                Remarks = subjectDto.Remarks?.Trim() ?? string.Empty
            };

            _context.Subjects.Add(subject);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Subject created successfully: {SubjectId} - {SubjectName}", subject.Id, subject.Name);
            return subject.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subject: {@SubjectDto}", subjectDto);
            throw;
        }
    }

    public async Task<bool> UpdateSubjectAsync(Guid id, UpdateSubjectDto subjectDto)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(id);
            if (subject == null)
            {
                _logger.LogWarning("Subject not found for update: {SubjectId}", id);
                return false;
            }

            // Validate unique constraints (excluding current subject)
            if (!await IsSubjectCodeUniqueAsync(subjectDto.Code, id))
            {
                throw new InvalidOperationException($"Subject code '{subjectDto.Code}' already exists.");
            }

            if (!await IsSubjectNameUniqueAsync(subjectDto.Name, id))
            {
                throw new InvalidOperationException($"Subject name '{subjectDto.Name}' already exists.");
            }

            // Update properties
            subject.Name = subjectDto.Name.Trim();
            subject.Code = subjectDto.Code.Trim().ToUpper();
            subject.Description = subjectDto.Description?.Trim() ?? string.Empty;
            subject.Category = subjectDto.Category;
            subject.Type = subjectDto.Type;
            subject.Credits = subjectDto.Credits;
            subject.WeeklyHours = subjectDto.WeeklyHours;
            subject.PracticalHours = subjectDto.PracticalHours;
            subject.TheoryHours = subjectDto.TheoryHours;
            subject.MaxMarks = subjectDto.MaxMarks;
            subject.PassingMarks = subjectDto.PassingMarks;
            subject.IsActive = subjectDto.IsActive;
            subject.IsMandatory = subjectDto.IsMandatory;
            subject.HasPractical = subjectDto.HasPractical;
            subject.DisplayOrder = subjectDto.DisplayOrder;
            subject.Icon = subjectDto.Icon?.Trim() ?? string.Empty;
            subject.Color = subjectDto.Color?.Trim() ?? string.Empty;
            subject.Prerequisites = subjectDto.Prerequisites?.Trim() ?? string.Empty;
            subject.LearningOutcomes = subjectDto.LearningOutcomes?.Trim() ?? string.Empty;
            subject.AssessmentMethods = subjectDto.AssessmentMethods?.Trim() ?? string.Empty;
            subject.Textbooks = subjectDto.Textbooks?.Trim() ?? string.Empty;
            subject.References = subjectDto.References?.Trim() ?? string.Empty;
            subject.Remarks = subjectDto.Remarks?.Trim() ?? string.Empty;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Subject updated successfully: {SubjectId} - {SubjectName}", subject.Id, subject.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subject: {SubjectId}, {@SubjectDto}", id, subjectDto);
            throw;
        }
    }

    public async Task<bool> DeleteSubjectAsync(Guid id)
    {
        try
        {
            if (!await CanDeleteSubjectAsync(id))
            {
                throw new InvalidOperationException("Cannot delete subject as it has dependencies.");
            }

            var subject = await _context.Subjects.FindAsync(id);
            if (subject == null)
            {
                _logger.LogWarning("Subject not found for deletion: {SubjectId}", id);
                return false;
            }

            _context.Subjects.Remove(subject);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Subject deleted successfully: {SubjectId} - {SubjectName}", subject.Id, subject.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting subject: {SubjectId}", id);
            throw;
        }
    }

    public async Task<bool> ActivateSubjectAsync(Guid id)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(id);
            if (subject == null)
            {
                _logger.LogWarning("Subject not found for activation: {SubjectId}", id);
                return false;
            }

            subject.IsActive = true;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Subject activated successfully: {SubjectId} - {SubjectName}", subject.Id, subject.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating subject: {SubjectId}", id);
            throw;
        }
    }

    public async Task<bool> DeactivateSubjectAsync(Guid id)
    {
        try
        {
            var subject = await _context.Subjects.FindAsync(id);
            if (subject == null)
            {
                _logger.LogWarning("Subject not found for deactivation: {SubjectId}", id);
                return false;
            }

            subject.IsActive = false;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Subject deactivated successfully: {SubjectId} - {SubjectName}", subject.Id, subject.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating subject: {SubjectId}", id);
            throw;
        }
    }

    #endregion

    #region Subject Validation

    public async Task<bool> IsSubjectCodeUniqueAsync(string code, Guid? excludeId = null)
    {
        try
        {
            var query = _context.Subjects.Where(s => s.Code == code.Trim().ToUpper());

            if (excludeId.HasValue)
            {
                query = query.Where(s => s.Id != excludeId.Value);
            }

            return !await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking subject code uniqueness: {Code}", code);
            throw;
        }
    }

    public async Task<bool> IsSubjectNameUniqueAsync(string name, Guid? excludeId = null)
    {
        try
        {
            var query = _context.Subjects.Where(s => s.Name == name.Trim());

            if (excludeId.HasValue)
            {
                query = query.Where(s => s.Id != excludeId.Value);
            }

            return !await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking subject name uniqueness: {Name}", name);
            throw;
        }
    }

    public async Task<bool> CanDeleteSubjectAsync(Guid id)
    {
        try
        {
            // Check if subject has any dependencies
            var hasGradeSubjects = await _context.GradeSubjects.AnyAsync(gs => gs.SubjectId == id);
            var hasFacultySubjects = await _context.FacultySubjects.AnyAsync(fs => fs.SubjectId == id);
            var hasTimetableEntries = await _context.TimetableEntries.AnyAsync(te => te.SubjectId == id);
            var hasExaminations = await _context.Examinations.AnyAsync(e => e.SubjectId == id);
            var hasStudentResults = await _context.StudentResults.AnyAsync(sr => sr.SubjectCode ==
                _context.Subjects.Where(s => s.Id == id).Select(s => s.Code).FirstOrDefault());

            return !(hasGradeSubjects || hasFacultySubjects || hasTimetableEntries || hasExaminations || hasStudentResults);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if subject can be deleted: {SubjectId}", id);
            throw;
        }
    }

    #endregion

    #region Grade-Subject Management

    public async Task<IEnumerable<GradeSubjectDto>> GetGradeSubjectsAsync(Guid gradeId)
    {
        try
        {
            var gradeSubjects = await _context.GradeSubjects
                .Where(gs => gs.GradeId == gradeId)
                .Include(gs => gs.Grade)
                .Include(gs => gs.Subject)
                .OrderBy(gs => gs.DisplayOrder)
                .ThenBy(gs => gs.Subject.Name)
                .Select(gs => new GradeSubjectDto
                {
                    Id = gs.Id,
                    GradeId = gs.GradeId,
                    SubjectId = gs.SubjectId,
                    GradeName = gs.Grade.Name,
                    SubjectName = gs.Subject.Name,
                    SubjectCode = gs.Subject.Code,
                    IsOptional = gs.IsOptional,
                    DisplayOrder = gs.DisplayOrder,
                    CustomMaxMarks = gs.CustomMaxMarks,
                    CustomPassingMarks = gs.CustomPassingMarks,
                    CustomWeeklyHours = gs.CustomWeeklyHours
                })
                .ToListAsync();

            return gradeSubjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving grade subjects: {GradeId}", gradeId);
            throw;
        }
    }

    public async Task<IEnumerable<SubjectDto>> GetSubjectsByGradeAsync(Guid gradeId)
    {
        try
        {
            var subjects = await _context.GradeSubjects
                .Where(gs => gs.GradeId == gradeId)
                .Include(gs => gs.Subject)
                .OrderBy(gs => gs.DisplayOrder)
                .ThenBy(gs => gs.Subject.Name)
                .Select(gs => new SubjectDto
                {
                    Id = gs.Subject.Id,
                    Name = gs.Subject.Name,
                    Code = gs.Subject.Code,
                    Description = gs.Subject.Description,
                    Category = gs.Subject.Category,
                    Type = gs.Subject.Type,
                    Credits = gs.Subject.Credits,
                    WeeklyHours = gs.CustomWeeklyHours ?? gs.Subject.WeeklyHours,
                    PracticalHours = gs.Subject.PracticalHours,
                    TheoryHours = gs.Subject.TheoryHours,
                    MaxMarks = gs.CustomMaxMarks ?? gs.Subject.MaxMarks,
                    PassingMarks = gs.CustomPassingMarks ?? gs.Subject.PassingMarks,
                    IsActive = gs.Subject.IsActive,
                    IsMandatory = gs.Subject.IsMandatory,
                    HasPractical = gs.Subject.HasPractical,
                    DisplayOrder = gs.DisplayOrder,
                    Icon = gs.Subject.Icon,
                    Color = gs.Subject.Color,
                    Prerequisites = gs.Subject.Prerequisites,
                    LearningOutcomes = gs.Subject.LearningOutcomes,
                    AssessmentMethods = gs.Subject.AssessmentMethods,
                    Textbooks = gs.Subject.Textbooks,
                    References = gs.Subject.References,
                    Remarks = gs.Subject.Remarks,
                    CreatedAt = gs.Subject.CreatedAt,
                    UpdatedAt = gs.Subject.LastModifiedAt
                })
                .ToListAsync();

            return subjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subjects by grade: {GradeId}", gradeId);
            throw;
        }
    }

    public async Task<bool> AssignSubjectToGradeAsync(CreateGradeSubjectDto gradeSubjectDto)
    {
        try
        {
            // Check if assignment already exists
            var existingAssignment = await _context.GradeSubjects
                .AnyAsync(gs => gs.GradeId == gradeSubjectDto.GradeId && gs.SubjectId == gradeSubjectDto.SubjectId);

            if (existingAssignment)
            {
                throw new InvalidOperationException("Subject is already assigned to this grade.");
            }

            // Verify grade and subject exist
            var gradeExists = await _context.Grades.AnyAsync(g => g.Id == gradeSubjectDto.GradeId);
            var subjectExists = await _context.Subjects.AnyAsync(s => s.Id == gradeSubjectDto.SubjectId);

            if (!gradeExists)
            {
                throw new InvalidOperationException("Grade not found.");
            }

            if (!subjectExists)
            {
                throw new InvalidOperationException("Subject not found.");
            }

            var tenantId = _tenantService.GetCurrentTenantId() ?? throw new InvalidOperationException("No tenant context available.");

            var gradeSubject = new GradeSubject
            {
                TenantId = tenantId,
                GradeId = gradeSubjectDto.GradeId,
                SubjectId = gradeSubjectDto.SubjectId,
                IsOptional = gradeSubjectDto.IsOptional,
                DisplayOrder = gradeSubjectDto.DisplayOrder,
                CustomMaxMarks = gradeSubjectDto.CustomMaxMarks,
                CustomPassingMarks = gradeSubjectDto.CustomPassingMarks,
                CustomWeeklyHours = gradeSubjectDto.CustomWeeklyHours
            };

            _context.GradeSubjects.Add(gradeSubject);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Subject assigned to grade successfully: Grade {GradeId}, Subject {SubjectId}",
                gradeSubjectDto.GradeId, gradeSubjectDto.SubjectId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning subject to grade: {@GradeSubjectDto}", gradeSubjectDto);
            throw;
        }
    }

    public async Task<bool> UnassignSubjectFromGradeAsync(Guid gradeId, Guid subjectId)
    {
        try
        {
            var gradeSubject = await _context.GradeSubjects
                .FirstOrDefaultAsync(gs => gs.GradeId == gradeId && gs.SubjectId == subjectId);

            if (gradeSubject == null)
            {
                _logger.LogWarning("Grade-Subject assignment not found: Grade {GradeId}, Subject {SubjectId}", gradeId, subjectId);
                return false;
            }

            // Check if there are any dependencies
            var hasTimetableEntries = await _context.TimetableEntries
                .AnyAsync(te => te.SubjectId == subjectId &&
                               te.Timetable.GradeId == gradeId);

            var hasExaminations = await _context.Examinations
                .AnyAsync(e => e.SubjectId == subjectId && e.GradeId == gradeId);

            if (hasTimetableEntries || hasExaminations)
            {
                throw new InvalidOperationException("Cannot unassign subject as it has dependencies (timetable entries or examinations).");
            }

            _context.GradeSubjects.Remove(gradeSubject);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Subject unassigned from grade successfully: Grade {GradeId}, Subject {SubjectId}", gradeId, subjectId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unassigning subject from grade: Grade {GradeId}, Subject {SubjectId}", gradeId, subjectId);
            throw;
        }
    }

    public async Task<bool> UpdateGradeSubjectAsync(Guid gradeId, Guid subjectId, CreateGradeSubjectDto gradeSubjectDto)
    {
        try
        {
            var gradeSubject = await _context.GradeSubjects
                .FirstOrDefaultAsync(gs => gs.GradeId == gradeId && gs.SubjectId == subjectId);

            if (gradeSubject == null)
            {
                _logger.LogWarning("Grade-Subject assignment not found for update: Grade {GradeId}, Subject {SubjectId}", gradeId, subjectId);
                return false;
            }

            gradeSubject.IsOptional = gradeSubjectDto.IsOptional;
            gradeSubject.DisplayOrder = gradeSubjectDto.DisplayOrder;
            gradeSubject.CustomMaxMarks = gradeSubjectDto.CustomMaxMarks;
            gradeSubject.CustomPassingMarks = gradeSubjectDto.CustomPassingMarks;
            gradeSubject.CustomWeeklyHours = gradeSubjectDto.CustomWeeklyHours;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Grade-Subject assignment updated successfully: Grade {GradeId}, Subject {SubjectId}", gradeId, subjectId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating grade-subject assignment: Grade {GradeId}, Subject {SubjectId}, {@GradeSubjectDto}",
                gradeId, subjectId, gradeSubjectDto);
            throw;
        }
    }

    #endregion

    #region Faculty-Subject Management

    public async Task<IEnumerable<FacultySubjectDto>> GetFacultySubjectsAsync(Guid facultyId, Guid? academicYearId = null)
    {
        try
        {
            var query = _context.FacultySubjects
                .Where(fs => fs.FacultyId == facultyId && fs.IsActive);

            if (academicYearId.HasValue)
            {
                query = query.Where(fs => fs.AcademicYearId == academicYearId.Value);
            }

            var facultySubjects = await query
                .Include(fs => fs.Faculty)
                .Include(fs => fs.Subject)
                .Include(fs => fs.Grade)
                .Include(fs => fs.Section)
                .Include(fs => fs.AcademicYear)
                .OrderBy(fs => fs.Subject.Name)
                .Select(fs => new FacultySubjectDto
                {
                    Id = fs.Id,
                    FacultyId = fs.FacultyId,
                    SubjectId = fs.SubjectId,
                    GradeId = fs.GradeId,
                    SectionId = fs.SectionId,
                    AcademicYearId = fs.AcademicYearId,
                    FacultyName = fs.Faculty.Name,
                    SubjectName = fs.Subject.Name,
                    SubjectCode = fs.Subject.Code,
                    GradeName = fs.Grade != null ? fs.Grade.Name : null,
                    SectionName = fs.Section != null ? fs.Section.Name : null,
                    AcademicYearName = fs.AcademicYear.Name,
                    IsPrimary = fs.IsPrimary,
                    AssignedDate = fs.AssignedDate,
                    UnassignedDate = fs.UnassignedDate,
                    IsActive = fs.IsActive
                })
                .ToListAsync();

            return facultySubjects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving faculty subjects: {FacultyId}, {AcademicYearId}", facultyId, academicYearId);
            throw;
        }
    }

    public async Task<IEnumerable<FacultySubjectDto>> GetSubjectFacultiesAsync(Guid subjectId, Guid? academicYearId = null)
    {
        try
        {
            var query = _context.FacultySubjects
                .Where(fs => fs.SubjectId == subjectId && fs.IsActive);

            if (academicYearId.HasValue)
            {
                query = query.Where(fs => fs.AcademicYearId == academicYearId.Value);
            }

            var subjectFaculties = await query
                .Include(fs => fs.Faculty)
                .Include(fs => fs.Subject)
                .Include(fs => fs.Grade)
                .Include(fs => fs.Section)
                .Include(fs => fs.AcademicYear)
                .OrderBy(fs => fs.Faculty.Name)
                .Select(fs => new FacultySubjectDto
                {
                    Id = fs.Id,
                    FacultyId = fs.FacultyId,
                    SubjectId = fs.SubjectId,
                    GradeId = fs.GradeId,
                    SectionId = fs.SectionId,
                    AcademicYearId = fs.AcademicYearId,
                    FacultyName = fs.Faculty.Name,
                    SubjectName = fs.Subject.Name,
                    SubjectCode = fs.Subject.Code,
                    GradeName = fs.Grade != null ? fs.Grade.Name : null,
                    SectionName = fs.Section != null ? fs.Section.Name : null,
                    AcademicYearName = fs.AcademicYear.Name,
                    IsPrimary = fs.IsPrimary,
                    AssignedDate = fs.AssignedDate,
                    UnassignedDate = fs.UnassignedDate,
                    IsActive = fs.IsActive
                })
                .ToListAsync();

            return subjectFaculties;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving subject faculties: {SubjectId}, {AcademicYearId}", subjectId, academicYearId);
            throw;
        }
    }

    public async Task<bool> AssignFacultyToSubjectAsync(CreateFacultySubjectDto facultySubjectDto)
    {
        try
        {
            // Check if assignment already exists
            var existingAssignment = await _context.FacultySubjects
                .AnyAsync(fs => fs.FacultyId == facultySubjectDto.FacultyId &&
                               fs.SubjectId == facultySubjectDto.SubjectId &&
                               fs.AcademicYearId == facultySubjectDto.AcademicYearId &&
                               fs.GradeId == facultySubjectDto.GradeId &&
                               fs.SectionId == facultySubjectDto.SectionId &&
                               fs.IsActive);

            if (existingAssignment)
            {
                throw new InvalidOperationException("Faculty is already assigned to this subject for the specified criteria.");
            }

            // Verify entities exist
            var facultyExists = await _context.Faculty.AnyAsync(f => f.Id == facultySubjectDto.FacultyId);
            var subjectExists = await _context.Subjects.AnyAsync(s => s.Id == facultySubjectDto.SubjectId);
            var academicYearExists = await _context.AcademicYears.AnyAsync(ay => ay.Id == facultySubjectDto.AcademicYearId);

            if (!facultyExists)
            {
                throw new InvalidOperationException("Faculty not found.");
            }

            if (!subjectExists)
            {
                throw new InvalidOperationException("Subject not found.");
            }

            if (!academicYearExists)
            {
                throw new InvalidOperationException("Academic year not found.");
            }

            if (facultySubjectDto.GradeId.HasValue)
            {
                var gradeExists = await _context.Grades.AnyAsync(g => g.Id == facultySubjectDto.GradeId.Value);
                if (!gradeExists)
                {
                    throw new InvalidOperationException("Grade not found.");
                }
            }

            if (facultySubjectDto.SectionId.HasValue)
            {
                var sectionExists = await _context.Sections.AnyAsync(s => s.Id == facultySubjectDto.SectionId.Value);
                if (!sectionExists)
                {
                    throw new InvalidOperationException("Section not found.");
                }
            }

            var tenantId = _tenantService.GetCurrentTenantId() ?? throw new InvalidOperationException("No tenant context available.");

            var facultySubject = new FacultySubject
            {
                TenantId = tenantId,
                FacultyId = facultySubjectDto.FacultyId,
                SubjectId = facultySubjectDto.SubjectId,
                GradeId = facultySubjectDto.GradeId,
                SectionId = facultySubjectDto.SectionId,
                AcademicYearId = facultySubjectDto.AcademicYearId,
                IsPrimary = facultySubjectDto.IsPrimary,
                AssignedDate = facultySubjectDto.AssignedDate,
                IsActive = facultySubjectDto.IsActive
            };

            _context.FacultySubjects.Add(facultySubject);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Faculty assigned to subject successfully: Faculty {FacultyId}, Subject {SubjectId}",
                facultySubjectDto.FacultyId, facultySubjectDto.SubjectId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning faculty to subject: {@FacultySubjectDto}", facultySubjectDto);
            throw;
        }
    }

    public async Task<bool> UnassignFacultyFromSubjectAsync(Guid facultyId, Guid subjectId, Guid academicYearId)
    {
        try
        {
            var facultySubject = await _context.FacultySubjects
                .FirstOrDefaultAsync(fs => fs.FacultyId == facultyId &&
                                          fs.SubjectId == subjectId &&
                                          fs.AcademicYearId == academicYearId &&
                                          fs.IsActive);

            if (facultySubject == null)
            {
                _logger.LogWarning("Faculty-Subject assignment not found: Faculty {FacultyId}, Subject {SubjectId}, AcademicYear {AcademicYearId}",
                    facultyId, subjectId, academicYearId);
                return false;
            }

            // Check if there are any dependencies (timetable entries)
            var hasTimetableEntries = await _context.TimetableEntries
                .AnyAsync(te => te.FacultyId == facultyId && te.SubjectId == subjectId);

            if (hasTimetableEntries)
            {
                // Instead of hard delete, mark as inactive and set unassigned date
                facultySubject.IsActive = false;
                facultySubject.UnassignedDate = DateTime.UtcNow;
            }
            else
            {
                _context.FacultySubjects.Remove(facultySubject);
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Faculty unassigned from subject successfully: Faculty {FacultyId}, Subject {SubjectId}",
                facultyId, subjectId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unassigning faculty from subject: Faculty {FacultyId}, Subject {SubjectId}, AcademicYear {AcademicYearId}",
                facultyId, subjectId, academicYearId);
            throw;
        }
    }

    public async Task<bool> UpdateFacultySubjectAsync(Guid id, CreateFacultySubjectDto facultySubjectDto)
    {
        try
        {
            var facultySubject = await _context.FacultySubjects.FindAsync(id);
            if (facultySubject == null)
            {
                _logger.LogWarning("Faculty-Subject assignment not found for update: {Id}", id);
                return false;
            }

            facultySubject.GradeId = facultySubjectDto.GradeId;
            facultySubject.SectionId = facultySubjectDto.SectionId;
            facultySubject.IsPrimary = facultySubjectDto.IsPrimary;
            facultySubject.IsActive = facultySubjectDto.IsActive;

            if (!facultySubjectDto.IsActive && facultySubject.UnassignedDate == null)
            {
                facultySubject.UnassignedDate = DateTime.UtcNow;
            }
            else if (facultySubjectDto.IsActive)
            {
                facultySubject.UnassignedDate = null;
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Faculty-Subject assignment updated successfully: {Id}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating faculty-subject assignment: {Id}, {@FacultySubjectDto}", id, facultySubjectDto);
            throw;
        }
    }

    #endregion
}
