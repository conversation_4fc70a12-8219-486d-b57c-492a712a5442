import { Compo<PERSON>, OnInit, HostListener, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBadgeModule } from '@angular/material/badge';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';

import { AuthService } from '../../../core/services/auth.service';
import { ThemeService } from '../../../core/services/theme.service';
import { LanguageThemeSwitcherComponent } from '../../../shared/components/ui/language-theme-switcher/language-theme-switcher.component';

// Define menu item interface
interface MenuItem {
  label: string;
  icon: string;
  route: string;
}

// Define menu category interface
interface MenuCategory {
  name: string;
  icon: string;
  items: MenuItem[];
  adminTypes?: ('SystemAdmin' | 'TenantAdmin')[]; // Which admin types can see this category
}

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule,
    MatBadgeModule,
    MatExpansionModule,
    TranslateModule,
    LanguageThemeSwitcherComponent
  ],
  templateUrl: './admin-layout.component.html',
  styleUrls: ['./admin-layout.component.scss']
})
export class AdminLayoutComponent implements OnInit {
  currentUser: any;
  sidebarCollapsed = false;
  isMobile = false;
  pageTitle = 'Dashboard';
  currentLanguage: string = 'en';
  expandedCategory: string | null = null;
  activeSubmenuCategory: string | null = null;
  activeMenuItem: string | null = null;

  // Local storage keys
  private readonly STORAGE_KEY_SIDEBAR = 'admin_sidebar_collapsed';
  private readonly STORAGE_KEY_EXPANDED_CATEGORY = 'admin_expanded_category';
  private readonly STORAGE_KEY_ACTIVE_MENU_ITEM = 'admin_active_menu_item';

  // All possible menu categories (will be filtered based on admin type)
  private allMenuCategories: MenuCategory[] = [
    {
      name: 'admin.categories.main',
      icon: 'home',
      adminTypes: ['SystemAdmin', 'TenantAdmin'], // Both admin types can see dashboard
      items: [
        {
          label: 'admin.menu.dashboard',
          icon: 'dashboard',
          route: '/admin'
        }
      ]
    },
    {
      name: 'admin.categories.content',
      icon: 'article',
      adminTypes: ['TenantAdmin'], // Only TenantAdmin manages school content
      items: [
        {
          label: 'admin.menu.notices',
          icon: 'announcement',
          route: '/admin/notices'
        },
        {
          label: 'admin.menu.news',
          icon: 'feed',
          route: '/admin/news'
        },
        {
          label: 'admin.menu.events',
          icon: 'event',
          route: '/admin/events'
        },
        {
          label: 'admin.menu.pages',
          icon: 'description',
          route: '/admin/pages'
        },
        {
          label: 'admin.menu.media',
          icon: 'perm_media',
          route: '/admin/media'
        }
      ]
    },
    {
      name: 'admin.categories.academic',
      icon: 'school',
      adminTypes: ['TenantAdmin'], // Only TenantAdmin manages academic content
      items: [
        {
          label: 'admin.menu.academic_years',
          icon: 'calendar_today',
          route: '/admin/academic-years'
        },
        {
          label: 'admin.menu.terms',
          icon: 'schedule',
          route: '/admin/terms'
        },
        {
          label: 'admin.menu.holidays',
          icon: 'event',
          route: '/admin/holidays'
        },
        {
          label: 'admin.menu.grades',
          icon: 'stairs',
          route: '/admin/grades'
        },
        {
          label: 'admin.menu.sections',
          icon: 'class',
          route: '/admin/sections'
        },
        {
          label: 'admin.menu.subjects',
          icon: 'subject',
          route: '/admin/subjects'
        },
        {
          label: 'admin.menu.class_teachers',
          icon: 'supervisor_account',
          route: '/admin/class-teachers'
        },
        {
          label: 'admin.menu.faculty',
          icon: 'groups',
          route: '/admin/faculty'
        },
        {
          label: 'admin.menu.courses',
          icon: 'menu_book',
          route: '/admin/courses'
        },
        {
          label: 'admin.menu.departments',
          icon: 'account_balance',
          route: '/admin/departments'
        },
        {
          label: 'admin.menu.admissions',
          icon: 'how_to_reg',
          route: '/admin/admissions'
        }
      ]
    },
    {
      name: 'admin.categories.campus',
      icon: 'location_city',
      adminTypes: ['TenantAdmin'], // Only TenantAdmin manages campus facilities
      items: [
        {
          label: 'admin.menu.facilities',
          icon: 'domain',
          route: '/admin/facilities'
        },
        {
          label: 'admin.menu.hostel',
          icon: 'night_shelter',
          route: '/admin/hostel'
        },
        {
          label: 'admin.menu.transportation',
          icon: 'directions_bus',
          route: '/admin/transportation'
        }
      ]
    },
    {
      name: 'admin.categories.users',
      icon: 'people',
      adminTypes: ['TenantAdmin'], // Only TenantAdmin manages school users
      items: [
        {
          label: 'admin.menu.students',
          icon: 'school',
          route: '/admin/students'
        },
        {
          label: 'admin.menu.parents',
          icon: 'family_restroom',
          route: '/admin/parents'
        },
        {
          label: 'admin.menu.staff',
          icon: 'badge',
          route: '/admin/staff'
        },
        {
          label: 'admin.menu.alumni',
          icon: 'history_edu',
          route: '/admin/alumni'
        },
        {
          label: 'admin.menu.users',
          icon: 'manage_accounts',
          route: '/admin/users'
        }
      ]
    },
    // SystemAdmin-specific categories
    {
      name: 'admin.categories.system',
      icon: 'admin_panel_settings',
      adminTypes: ['SystemAdmin'], // Only SystemAdmin can see system management
      items: [
        {
          label: 'admin.menu.tenants',
          icon: 'business',
          route: '/admin/tenants'
        },
        {
          label: 'admin.menu.organizations',
          icon: 'corporate_fare',
          route: '/admin/organizations'
        },
        {
          label: 'admin.menu.system_users',
          icon: 'supervisor_account',
          route: '/admin/system-users'
        },
        {
          label: 'admin.menu.system_logs',
          icon: 'description',
          route: '/admin/system-logs'
        }
      ]
    },
    {
      name: 'admin.categories.platform',
      icon: 'cloud',
      adminTypes: ['SystemAdmin'], // Only SystemAdmin manages platform
      items: [
        {
          label: 'admin.menu.subscriptions',
          icon: 'payment',
          route: '/admin/subscriptions'
        },
        {
          label: 'admin.menu.licenses',
          icon: 'verified',
          route: '/admin/licenses'
        },
        {
          label: 'admin.menu.analytics',
          icon: 'analytics',
          route: '/admin/analytics'
        },
        {
          label: 'admin.menu.monitoring',
          icon: 'monitor_heart',
          route: '/admin/monitoring'
        }
      ]
    },
    {
      name: 'admin.categories.settings',
      icon: 'settings',
      adminTypes: ['SystemAdmin', 'TenantAdmin'], // Both can access settings (different scopes)
      items: [
        {
          label: 'admin.menu.general',
          icon: 'tune',
          route: '/admin/settings/general'
        },
        {
          label: 'admin.menu.appearance',
          icon: 'palette',
          route: '/admin/settings/appearance'
        },
        {
          label: 'admin.menu.translations',
          icon: 'translate',
          route: '/admin/settings/translations'
        }
      ]
    }
  ];

  // Filtered menu categories based on current admin type
  menuCategories: MenuCategory[] = [];

  // Flattened menu items for page title lookup
  menuItems: MenuItem[] = [];

  constructor(
    public authService: AuthService,
    private translate: TranslateService,
    private router: Router
  ) {
    this.currentLanguage = this.translate.currentLang || 'en';
    this.checkScreenSize();
    this.initializeMenus();
  }

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();

    // Load state from localStorage
    this.loadStateFromStorage();

    // Update page title and active menu item based on route
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.updatePageTitle();
      this.setActiveMenuItemFromRoute();
    });

    // Initial page title and active menu item
    this.updatePageTitle();

    // Only set active menu item from route if not already loaded from storage
    if (!this.activeMenuItem) {
      this.setActiveMenuItemFromRoute();
    }
  }

  /**
   * Load state from localStorage
   */
  private loadStateFromStorage(): void {
    try {
      // Load sidebar collapsed state
      const sidebarState = localStorage.getItem(this.STORAGE_KEY_SIDEBAR);
      if (sidebarState !== null) {
        this.sidebarCollapsed = sidebarState === 'true';
      }

      // Don't load expanded category if sidebar is collapsed
      if (!this.sidebarCollapsed) {
        // Load expanded category
        const expandedCategory = localStorage.getItem(this.STORAGE_KEY_EXPANDED_CATEGORY);
        if (expandedCategory !== null) {
          this.expandedCategory = expandedCategory;
        }
      }

      // Load active menu item
      const activeMenuItem = localStorage.getItem(this.STORAGE_KEY_ACTIVE_MENU_ITEM);
      if (activeMenuItem !== null) {
        this.activeMenuItem = activeMenuItem;
      }
    } catch (error) {
      console.error('Error loading state from localStorage:', error);
    }
  }

  /**
   * Save state to localStorage
   */
  private saveStateToStorage(): void {
    try {
      // Save sidebar collapsed state
      localStorage.setItem(this.STORAGE_KEY_SIDEBAR, this.sidebarCollapsed.toString());

      // Save expanded category
      if (this.expandedCategory) {
        localStorage.setItem(this.STORAGE_KEY_EXPANDED_CATEGORY, this.expandedCategory);
      } else {
        localStorage.removeItem(this.STORAGE_KEY_EXPANDED_CATEGORY);
      }

      // Save active menu item
      if (this.activeMenuItem) {
        localStorage.setItem(this.STORAGE_KEY_ACTIVE_MENU_ITEM, this.activeMenuItem);
      } else {
        localStorage.removeItem(this.STORAGE_KEY_ACTIVE_MENU_ITEM);
      }
    } catch (error) {
      console.error('Error saving state to localStorage:', error);
    }
  }

  /**
   * Set active menu item based on current route
   */
  private setActiveMenuItemFromRoute(): void {
    const currentRoute = this.router.url;

    // Find the matching menu item for the current route
    const matchingItem = this.menuItems.find(item => {
      // Exact match
      if (item.route === currentRoute) {
        return true;
      }
      // Partial match for nested routes
      if (currentRoute.startsWith(item.route + '/')) {
        return true;
      }
      return false;
    });

    if (matchingItem) {
      // Set the active menu item
      this.activeMenuItem = matchingItem.route;

      // Clear any active submenu
      this.activeSubmenuCategory = null;
    } else if (currentRoute === '/admin') {
      // Dashboard is active
      this.activeMenuItem = '/admin';
      this.activeSubmenuCategory = null;
    } else {
      // No active menu item
      this.activeMenuItem = null;
      this.activeSubmenuCategory = null;
    }

    // Save state to localStorage
    this.saveStateToStorage();
  }

  @HostListener('window:resize')
  checkScreenSize(): void {
    this.isMobile = window.innerWidth < 992;
    if (this.isMobile) {
      this.sidebarCollapsed = true;
    }
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;

    // Close all expanded categories when collapsing sidebar
    if (this.sidebarCollapsed) {
      this.expandedCategory = null;
    }

    // Save state to localStorage
    this.saveStateToStorage();
  }

  /**
   * Toggle a category's expansion state
   */
  toggleCategory(categoryName: string): void {
    if (this.sidebarCollapsed) {
      return; // Don't toggle when sidebar is collapsed
    }

    if (this.expandedCategory === categoryName) {
      this.expandedCategory = null; // Close if already open
    } else {
      this.expandedCategory = categoryName; // Open the clicked category
    }

    // Save state to localStorage
    this.saveStateToStorage();
  }

  /**
   * Check if a category is expanded
   */
  isCategoryExpanded(categoryName: string): boolean {
    return this.expandedCategory === categoryName;
  }

  /**
   * Check if a submenu is active
   */
  isSubmenuActive(categoryName: string): boolean {
    return this.activeSubmenuCategory === categoryName;
  }

  /**
   * Check if a menu item is active
   */
  isMenuItemActive(route: string): boolean {
    return this.activeMenuItem === route;
  }

  /**
   * Set active menu item
   */
  setActiveMenuItem(route: string): void {
    // Clear any active submenu
    this.hideAllSubmenus();

    // Set the active menu item
    this.activeMenuItem = route;

    // Save state to localStorage
    this.saveStateToStorage();
  }

  /**
   * Show submenu when hovering over a category in collapsed mode
   */
  showSubmenu(event: Event, categoryName: string): void {
    if (this.sidebarCollapsed) {
      // Prevent default behavior
      event.preventDefault();
      event.stopPropagation();

      // First, hide all submenus
      this.hideAllSubmenus();

      // Find the hover-submenu element
      const target = event.currentTarget as HTMLElement;
      const submenu = target.parentElement?.querySelector('.hover-submenu') as HTMLElement;

      if (submenu) {
        // Clear any previously active menu item
        this.activeMenuItem = null;

        // Store the currently active submenu
        this.activeSubmenuCategory = categoryName;

        // Position the submenu correctly
        const targetRect = target.getBoundingClientRect();

        // Set the top position to align with the menu item
        submenu.style.top = `${targetRect.top}px`;

        // Set the left position to be right next to the collapsed sidebar
        const sidebarWidth = this.sidebarCollapsed ? 70 : 260; // $sidebar-collapsed-width or $sidebar-width
        submenu.style.left = `${sidebarWidth}px`;

        // Show the submenu
        submenu.style.display = 'block';
        submenu.style.visibility = 'visible';
        submenu.style.opacity = '1';
        submenu.style.transform = 'translateX(0)';

        // Ensure the submenu is fully visible
        const submenuRect = submenu.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        // If submenu extends beyond viewport bottom, adjust its position
        if (submenuRect.bottom > viewportHeight) {
          const overflow = submenuRect.bottom - viewportHeight;
          submenu.style.top = `${targetRect.top - overflow - 10}px`; // 10px buffer
        }
      }
    }
  }

  /**
   * Hide all submenus
   */
  private hideAllSubmenus(): void {
    if (this.sidebarCollapsed) {
      const submenus = document.querySelectorAll('.hover-submenu');
      submenus.forEach(submenu => {
        const element = submenu as HTMLElement;
        element.style.display = 'none';
        element.style.visibility = 'hidden';
        element.style.opacity = '0';
        element.style.transform = 'translateX(-20px)';
      });

      // Clear the active submenu and menu item
      this.activeSubmenuCategory = null;
      this.activeMenuItem = null;
    }
  }

  /**
   * Hide submenu when mouse leaves
   */
  hideSubmenu(event: Event): void {
    if (this.sidebarCollapsed) {
      const submenu = event.currentTarget as HTMLElement;

      if (submenu) {
        // Add a small delay to allow moving to the submenu
        setTimeout(() => {
          // Check if the mouse is still outside the submenu
          if (!submenu.matches(':hover')) {
            // Hide the submenu
            submenu.style.display = 'none';
            submenu.style.visibility = 'hidden';
            submenu.style.opacity = '0';
            submenu.style.transform = 'translateX(-20px)';

            // Clear the active submenu and menu item
            this.activeSubmenuCategory = null;
            this.activeMenuItem = null;
          }
        }, 100);
      }
    }
  }

  logout(): void {
    this.authService.logout();
  }

  switchLanguage(lang: string): void {
    this.translate.use(lang);
    this.currentLanguage = lang;
  }

  private updatePageTitle(): void {
    const currentRoute = this.router.url;

    // Find the matching menu item for the current route
    const matchingItem = this.menuItems.find(item => {
      // Exact match
      if (item.route === currentRoute) {
        return true;
      }
      // Partial match for nested routes
      if (currentRoute.startsWith(item.route + '/')) {
        return true;
      }
      return false;
    });

    if (matchingItem) {
      // Use the translated label without the prefix
      const label = this.translate.instant(matchingItem.label);
      this.pageTitle = label;
    } else if (currentRoute === '/admin') {
      this.pageTitle = 'Dashboard';
    } else {
      // Default title if no match found
      this.pageTitle = 'Admin Panel';
    }
  }

  /**
   * Initialize menus based on current admin type
   */
  private initializeMenus(): void {
    const adminType = this.getCurrentAdminType();
    this.menuCategories = this.filterMenusByAdminType(adminType);
    this.menuItems = this.menuCategories.reduce((items: MenuItem[], category) => {
      return [...items, ...category.items];
    }, []);
  }

  /**
   * Get current admin type from auth service
   */
  private getCurrentAdminType(): 'SystemAdmin' | 'TenantAdmin' {
    if (this.authService.isSystemAdmin()) {
      return 'SystemAdmin';
    }
    return 'TenantAdmin'; // Default to TenantAdmin for regular admins
  }

  /**
   * Filter menu categories based on admin type
   */
  private filterMenusByAdminType(adminType: 'SystemAdmin' | 'TenantAdmin'): MenuCategory[] {
    return this.allMenuCategories.filter(category => {
      // If no adminTypes specified, show to all admin types
      if (!category.adminTypes || category.adminTypes.length === 0) {
        return true;
      }
      // Check if current admin type is allowed for this category
      return category.adminTypes.includes(adminType);
    });
  }

  /**
   * Check if current user is SystemAdmin
   */
  isSystemAdmin(): boolean {
    return this.authService.isSystemAdmin();
  }

  /**
   * Check if current user is TenantAdmin
   */
  isTenantAdmin(): boolean {
    return this.authService.isTenantAdmin();
  }

  /**
   * Get admin type display name
   */
  getAdminTypeDisplayName(): string {
    if (this.isSystemAdmin()) {
      return 'System Administrator';
    }
    return 'School Administrator';
  }
}
