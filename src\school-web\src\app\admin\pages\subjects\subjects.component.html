<div class="subjects-container">
  <!-- Header -->
  <mat-toolbar class="page-header">
    <mat-icon>subject</mat-icon>
    <span class="page-title">{{ 'ADMIN.SUBJECTS.TITLE' | translate }}</span>
    <span class="spacer"></span>
    <button mat-raised-button color="primary" (click)="openCreateDialog()">
      <mat-icon>add</mat-icon>
      {{ 'ADMIN.SUBJECTS.CREATE' | translate }}
    </button>
  </mat-toolbar>

  <!-- Filters Card -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-row">
        <!-- Search -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>{{ 'COMMON.SEARCH' | translate }}</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup.enter)="onSearch()" 
                 placeholder="{{ 'ADMIN.SUBJECTS.SEARCH_PLACEHOLDER' | translate }}">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Grade Filter -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'ADMIN.SUBJECTS.GRADE' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedGrade" (selectionChange)="onFilterChange()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let grade of grades" [value]="grade.id">
              {{ grade.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Academic Year Filter -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'ADMIN.SUBJECTS.ACADEMIC_YEAR' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedAcademicYear" (selectionChange)="onFilterChange()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let year of academicYears" [value]="year.id">
              {{ year.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Subject Type Filter -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'ADMIN.SUBJECTS.TYPE' | translate }}</mat-label>
          <mat-select [(ngModel)]="selectedSubjectType" (selectionChange)="onFilterChange()">
            <mat-option value="">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option *ngFor="let type of subjectTypes" [value]="type">
              {{ 'ADMIN.SUBJECTS.TYPES.' + type | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Status Filter -->
        <mat-form-field appearance="outline">
          <mat-label>{{ 'COMMON.STATUS' | translate }}</mat-label>
          <mat-select [(ngModel)]="isActiveFilter" (selectionChange)="onFilterChange()">
            <mat-option [value]="null">{{ 'COMMON.ALL' | translate }}</mat-option>
            <mat-option [value]="true">{{ 'COMMON.ACTIVE' | translate }}</mat-option>
            <mat-option [value]="false">{{ 'COMMON.INACTIVE' | translate }}</mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Action Buttons -->
        <div class="filter-actions">
          <button mat-button (click)="onSearch()">
            <mat-icon>search</mat-icon>
            {{ 'COMMON.SEARCH' | translate }}
          </button>
          <button mat-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            {{ 'COMMON.CLEAR' | translate }}
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Data Table Card -->
  <mat-card class="table-card">
    <mat-card-content>
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p>{{ 'COMMON.LOADING' | translate }}</p>
      </div>

      <!-- Data Table -->
      <div *ngIf="!loading" class="table-container">
        <table mat-table [dataSource]="subjects" class="subjects-table" matSort>
          <!-- Code Column -->
          <ng-container matColumnDef="code">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'ADMIN.SUBJECTS.CODE' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <span class="subject-code">{{ subject.code }}</span>
            </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'ADMIN.SUBJECTS.NAME' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <div class="subject-name-cell">
                <span class="subject-name">{{ subject.name }}</span>
                <span class="subject-description" *ngIf="subject.shortDescription">
                  {{ subject.shortDescription }}
                </span>
              </div>
            </td>
          </ng-container>

          <!-- Subject Type Column -->
          <ng-container matColumnDef="subjectType">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'ADMIN.SUBJECTS.TYPE' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <mat-chip [color]="getSubjectTypeColor(subject.subjectType)" selected>
                {{ 'ADMIN.SUBJECTS.TYPES.' + subject.subjectType | translate }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Credits Column -->
          <ng-container matColumnDef="credits">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'ADMIN.SUBJECTS.CREDITS' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <span class="credits-badge">{{ subject.credits }}</span>
            </td>
          </ng-container>

          <!-- Hours Per Week Column -->
          <ng-container matColumnDef="hoursPerWeek">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              {{ 'ADMIN.SUBJECTS.HOURS_PER_WEEK' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <span class="hours-badge">{{ subject.hoursPerWeek }}h</span>
            </td>
          </ng-container>

          <!-- Grade Column -->
          <ng-container matColumnDef="gradeName">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'ADMIN.SUBJECTS.GRADE' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <span class="grade-name">{{ subject.gradeName }}</span>
            </td>
          </ng-container>

          <!-- Department Column -->
          <ng-container matColumnDef="departmentName">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'ADMIN.SUBJECTS.DEPARTMENT' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <span class="department-name">{{ subject.departmentName }}</span>
            </td>
          </ng-container>

          <!-- Is Core Column -->
          <ng-container matColumnDef="isCore">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'ADMIN.SUBJECTS.IS_CORE' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <mat-chip *ngIf="subject.isCore" color="primary" selected>
                {{ 'ADMIN.SUBJECTS.CORE' | translate }}
              </mat-chip>
              <mat-chip *ngIf="subject.isElective" color="accent" selected>
                {{ 'ADMIN.SUBJECTS.ELECTIVE' | translate }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.STATUS' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <mat-slide-toggle 
                [checked]="subject.isActive"
                (change)="toggleSubjectStatus(subject)"
                [matTooltip]="subject.isActive ? ('COMMON.DEACTIVATE' | translate) : ('COMMON.ACTIVATE' | translate)">
              </mat-slide-toggle>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>
              {{ 'COMMON.ACTIONS' | translate }}
            </th>
            <td mat-cell *matCellDef="let subject">
              <button mat-icon-button [matMenuTriggerFor]="actionMenu" 
                      [matTooltip]="'COMMON.ACTIONS' | translate">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #actionMenu="matMenu">
                <button mat-menu-item (click)="openEditDialog(subject)">
                  <mat-icon>edit</mat-icon>
                  <span>{{ 'COMMON.EDIT' | translate }}</span>
                </button>
                <button mat-menu-item (click)="deleteSubject(subject)">
                  <mat-icon color="warn">delete</mat-icon>
                  <span>{{ 'COMMON.DELETE' | translate }}</span>
                </button>
              </mat-menu>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No Data Message -->
        <div *ngIf="subjects.length === 0" class="no-data">
          <mat-icon>subject</mat-icon>
          <h3>{{ 'ADMIN.SUBJECTS.NO_DATA' | translate }}</h3>
          <p>{{ 'ADMIN.SUBJECTS.NO_DATA_MESSAGE' | translate }}</p>
        </div>
      </div>

      <!-- Paginator -->
      <mat-paginator 
        *ngIf="!loading && subjects.length > 0"
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50, 100]"
        [pageIndex]="pageIndex"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
