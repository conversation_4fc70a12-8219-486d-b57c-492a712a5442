.subjects-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    .page-title {
      font-size: 1.5rem;
      font-weight: 500;
      margin-left: 10px;
    }

    .spacer {
      flex: 1 1 auto;
    }

    button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .filters-card {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .filters-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;

      .search-field {
        flex: 1;
        min-width: 250px;
      }

      mat-form-field {
        min-width: 150px;
      }

      .filter-actions {
        display: flex;
        gap: 8px;
        margin-left: auto;

        button {
          min-width: 100px;
        }
      }
    }
  }

  .table-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      
      mat-spinner {
        margin-bottom: 20px;
      }

      p {
        color: #666;
        font-size: 1.1rem;
      }
    }

    .table-container {
      overflow-x: auto;

      .subjects-table {
        width: 100%;
        
        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #495057;
          border-bottom: 2px solid #dee2e6;
        }

        td {
          border-bottom: 1px solid #dee2e6;
          padding: 12px 8px;
        }

        .subject-code {
          font-family: 'Courier New', monospace;
          background: #e9ecef;
          padding: 4px 8px;
          border-radius: 4px;
          font-weight: 600;
          color: #495057;
        }

        .subject-name-cell {
          .subject-name {
            display: block;
            font-weight: 500;
            color: #212529;
            margin-bottom: 2px;
          }

          .subject-description {
            display: block;
            font-size: 0.85rem;
            color: #6c757d;
            font-style: italic;
          }
        }

        .credits-badge,
        .hours-badge {
          background: #007bff;
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.85rem;
          font-weight: 500;
        }

        .hours-badge {
          background: #28a745;
        }

        .grade-name,
        .department-name {
          color: #495057;
          font-weight: 500;
        }

        mat-chip {
          font-size: 0.8rem;
          font-weight: 500;
        }

        mat-slide-toggle {
          transform: scale(0.8);
        }

        .mat-mdc-icon-button {
          width: 36px;
          height: 36px;
          
          mat-icon {
            font-size: 18px;
          }
        }
      }
    }

    .no-data {
      text-align: center;
      padding: 60px 20px;
      color: #6c757d;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      h3 {
        margin: 0 0 8px 0;
        font-weight: 500;
      }

      p {
        margin: 0;
        font-size: 0.95rem;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .subjects-container {
    padding: 10px;

    .page-header {
      .page-title {
        font-size: 1.2rem;
      }
    }

    .filters-card {
      .filters-row {
        flex-direction: column;
        align-items: stretch;

        .search-field,
        mat-form-field {
          min-width: unset;
          width: 100%;
        }

        .filter-actions {
          margin-left: 0;
          justify-content: center;
        }
      }
    }

    .table-container {
      .subjects-table {
        font-size: 0.9rem;

        th, td {
          padding: 8px 4px;
        }
      }
    }
  }
}

// Dark Theme Support
.dark-theme {
  .subjects-container {
    .page-header {
      background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    }

    .filters-card,
    .table-card {
      background: #2d3748;
      color: #e2e8f0;

      .subjects-table {
        th {
          background-color: #4a5568;
          color: #e2e8f0;
          border-bottom-color: #4a5568;
        }

        td {
          border-bottom-color: #4a5568;
        }

        .subject-code {
          background: #4a5568;
          color: #e2e8f0;
        }

        .subject-name {
          color: #e2e8f0;
        }

        .subject-description {
          color: #a0aec0;
        }

        .grade-name,
        .department-name {
          color: #e2e8f0;
        }
      }

      .no-data {
        color: #a0aec0;
      }
    }
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
