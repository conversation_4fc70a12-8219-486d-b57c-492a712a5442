<div class="tenant-analytics" *ngIf="tenantStats">
  <!-- Overview Cards -->
  <div class="analytics-overview">
    <mat-card class="overview-card">
      <mat-card-header>
        <mat-card-title>{{ 'admin.tenant_analytics.overview' | translate }}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="overview-grid">
          <div class="overview-item">
            <mat-icon class="overview-icon users">people</mat-icon>
            <div class="overview-content">
              <div class="overview-value">{{ tenantStats.totalUsers }}</div>
              <div class="overview-label">{{ 'admin.tenant_analytics.total_users' | translate }}</div>
            </div>
          </div>

          <div class="overview-item">
            <mat-icon class="overview-icon students">school</mat-icon>
            <div class="overview-content">
              <div class="overview-value">{{ tenantStats.totalStudents }}</div>
              <div class="overview-label">{{ 'admin.tenant_analytics.students' | translate }}</div>
            </div>
          </div>

          <div class="overview-item">
            <mat-icon class="overview-icon faculty">person</mat-icon>
            <div class="overview-content">
              <div class="overview-value">{{ tenantStats.totalFaculty }}</div>
              <div class="overview-label">{{ 'admin.tenant_analytics.faculty' | translate }}</div>
            </div>
          </div>

          <div class="overview-item">
            <mat-icon class="overview-icon parents">family_restroom</mat-icon>
            <div class="overview-content">
              <div class="overview-value">{{ tenantStats.totalParents }}</div>
              <div class="overview-label">{{ 'admin.tenant_analytics.parents' | translate }}</div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Activity Metrics -->
    <mat-card class="activity-card">
      <mat-card-header>
        <mat-card-title>{{ 'admin.tenant_analytics.activity_metrics' | translate }}</mat-card-title>
        <mat-icon [color]="getTrendColor()" class="trend-icon">{{ getTrendIcon() }}</mat-icon>
      </mat-card-header>
      <mat-card-content>
        <div class="activity-metrics">
          <div class="metric-item">
            <div class="metric-value">{{ tenantStats.activeStudentsThisMonth }}</div>
            <div class="metric-label">{{ 'ADMIN.TENANT_ANALYTICS.ACTIVE_THIS_MONTH' | translate }}</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">{{ tenantStats.newStudentsThisMonth }}</div>
            <div class="metric-label">{{ 'ADMIN.TENANT_ANALYTICS.NEW_THIS_MONTH' | translate }}</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Detailed Analytics Tabs -->
  <mat-tab-group class="analytics-tabs">
    <!-- Academic Structure Tab -->
    <mat-tab [label]="'ADMIN.TENANT_ANALYTICS.ACADEMIC_STRUCTURE' | translate">
      <div class="tab-content">
        <div class="stats-grid">
          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-item">
                <mat-icon>class</mat-icon>
                <div class="stat-details">
                  <div class="stat-value">{{ tenantStats.totalGrades }}</div>
                  <div class="stat-label">{{ 'ADMIN.TENANT_ANALYTICS.GRADES' | translate }}</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-item">
                <mat-icon>group_work</mat-icon>
                <div class="stat-details">
                  <div class="stat-value">{{ tenantStats.totalSections }}</div>
                  <div class="stat-label">{{ 'ADMIN.TENANT_ANALYTICS.SECTIONS' | translate }}</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-item">
                <mat-icon>book</mat-icon>
                <div class="stat-details">
                  <div class="stat-value">{{ tenantStats.totalSubjects }}</div>
                  <div class="stat-label">{{ 'ADMIN.TENANT_ANALYTICS.SUBJECTS' | translate }}</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-item">
                <mat-icon>calendar_today</mat-icon>
                <div class="stat-details">
                  <div class="stat-value">{{ tenantStats.totalAcademicYears }}</div>
                  <div class="stat-label">{{ 'ADMIN.TENANT_ANALYTICS.ACADEMIC_YEARS' | translate }}</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-item">
                <mat-icon>schedule</mat-icon>
                <div class="stat-details">
                  <div class="stat-value">{{ tenantStats.totalTerms }}</div>
                  <div class="stat-label">{{ 'ADMIN.TENANT_ANALYTICS.TERMS' | translate }}</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <!-- Content & Communication Tab -->
    <mat-tab [label]="'ADMIN.TENANT_ANALYTICS.CONTENT_COMMUNICATION' | translate">
      <div class="tab-content">
        <div class="stats-grid">
          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-item">
                <mat-icon>announcement</mat-icon>
                <div class="stat-details">
                  <div class="stat-value">{{ tenantStats.totalNotices }}</div>
                  <div class="stat-label">{{ 'ADMIN.TENANT_ANALYTICS.NOTICES' | translate }}</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <mat-card class="stat-card">
            <mat-card-content>
              <div class="stat-item">
                <mat-icon>event</mat-icon>
                <div class="stat-details">
                  <div class="stat-value">{{ tenantStats.totalEvents }}</div>
                  <div class="stat-label">{{ 'ADMIN.TENANT_ANALYTICS.EVENTS' | translate }}</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <!-- Usage & Limits Tab -->
    <mat-tab [label]="'ADMIN.TENANT_ANALYTICS.USAGE_LIMITS' | translate">
      <div class="tab-content">
        <div class="usage-cards">
          <!-- Storage Usage -->
          <mat-card class="usage-card" *ngIf="tenantStats.maxStorageMB">
            <mat-card-header>
              <mat-card-title>{{ 'ADMIN.TENANT_ANALYTICS.STORAGE_USAGE' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="usage-info">
                <div class="usage-text">
                  {{ formatStorageSize(tenantStats.storageUsedMB) }} / {{ formatStorageSize(tenantStats.maxStorageMB) }}
                </div>
                <div class="usage-percentage">{{ getStorageUsagePercentage() | number:'1.0-1' }}%</div>
              </div>
              <mat-progress-bar 
                [value]="getStorageUsagePercentage()" 
                [color]="getUsageColor(getStorageUsagePercentage())">
              </mat-progress-bar>
            </mat-card-content>
          </mat-card>

          <!-- Student Limit -->
          <mat-card class="usage-card" *ngIf="tenantStats.maxStudents">
            <mat-card-header>
              <mat-card-title>{{ 'ADMIN.TENANT_ANALYTICS.STUDENT_LIMIT' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="usage-info">
                <div class="usage-text">
                  {{ tenantStats.totalStudents }} / {{ tenantStats.maxStudents }}
                </div>
                <div class="usage-percentage">{{ getStudentUsagePercentage() | number:'1.0-1' }}%</div>
              </div>
              <mat-progress-bar 
                [value]="getStudentUsagePercentage()" 
                [color]="getUsageColor(getStudentUsagePercentage())">
              </mat-progress-bar>
            </mat-card-content>
          </mat-card>

          <!-- Faculty Limit -->
          <mat-card class="usage-card" *ngIf="tenantStats.maxFaculty">
            <mat-card-header>
              <mat-card-title>{{ 'ADMIN.TENANT_ANALYTICS.FACULTY_LIMIT' | translate }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="usage-info">
                <div class="usage-text">
                  {{ tenantStats.totalFaculty }} / {{ tenantStats.maxFaculty }}
                </div>
                <div class="usage-percentage">{{ getFacultyUsagePercentage() | number:'1.0-1' }}%</div>
              </div>
              <mat-progress-bar 
                [value]="getFacultyUsagePercentage()" 
                [color]="getUsageColor(getFacultyUsagePercentage())">
              </mat-progress-bar>
            </mat-card-content>
          </mat-card>
        </div>

        <!-- Trial Information -->
        <mat-card class="trial-card" *ngIf="tenantStats.isTrialActive">
          <mat-card-header>
            <mat-card-title>{{ 'ADMIN.TENANT_ANALYTICS.TRIAL_STATUS' | translate }}</mat-card-title>
            <mat-chip color="accent">{{ 'ADMIN.TENANT_ANALYTICS.TRIAL_ACTIVE' | translate }}</mat-chip>
          </mat-card-header>
          <mat-card-content>
            <div class="trial-info">
              <div class="trial-days">
                <span class="days-number">{{ getTrialDaysRemaining() }}</span>
                <span class="days-label">{{ 'ADMIN.TENANT_ANALYTICS.DAYS_REMAINING' | translate }}</span>
              </div>
              <div class="trial-end-date">
                {{ 'ADMIN.TENANT_ANALYTICS.TRIAL_ENDS' | translate }}: 
                {{ tenantStats.trialEndDate | date:'mediumDate' }}
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
