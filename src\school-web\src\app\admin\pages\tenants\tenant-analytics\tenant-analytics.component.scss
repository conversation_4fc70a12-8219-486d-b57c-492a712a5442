.tenant-analytics {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .analytics-overview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .overview-card {
      .overview-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;

        @media (max-width: 480px) {
          grid-template-columns: 1fr;
        }

        .overview-item {
          display: flex;
          align-items: center;
          gap: 16px;

          .overview-icon {
            font-size: 32px;
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;

            &.users {
              background-color: rgba(33, 150, 243, 0.1);
              color: #2196f3;
            }

            &.students {
              background-color: rgba(76, 175, 80, 0.1);
              color: #4caf50;
            }

            &.faculty {
              background-color: rgba(255, 152, 0, 0.1);
              color: #ff9800;
            }

            &.parents {
              background-color: rgba(156, 39, 176, 0.1);
              color: #9c27b0;
            }
          }

          .overview-content {
            .overview-value {
              font-size: 24px;
              font-weight: 600;
              line-height: 1.2;
            }

            .overview-label {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.6);
              margin-top: 4px;
            }
          }
        }
      }
    }

    .activity-card {
      .trend-icon {
        margin-left: auto;
      }

      .activity-metrics {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .metric-item {
          text-align: center;
          padding: 16px;
          border-radius: 8px;
          background-color: rgba(0, 0, 0, 0.02);

          .metric-value {
            font-size: 28px;
            font-weight: 600;
            color: #2196f3;
          }

          .metric-label {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
            margin-top: 4px;
          }
        }
      }
    }
  }

  .analytics-tabs {
    .tab-content {
      padding: 24px 0;

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;

        .stat-card {
          .stat-item {
            display: flex;
            align-items: center;
            gap: 16px;

            mat-icon {
              font-size: 24px;
              width: 40px;
              height: 40px;
              border-radius: 8px;
              background-color: rgba(33, 150, 243, 0.1);
              color: #2196f3;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .stat-details {
              .stat-value {
                font-size: 20px;
                font-weight: 600;
                line-height: 1.2;
              }

              .stat-label {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.6);
                margin-top: 2px;
              }
            }
          }
        }
      }

      .usage-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;

        .usage-card {
          .usage-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .usage-text {
              font-size: 16px;
              font-weight: 500;
            }

            .usage-percentage {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.6);
            }
          }

          mat-progress-bar {
            height: 8px;
            border-radius: 4px;
          }
        }

        .trial-card {
          grid-column: 1 / -1;

          mat-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .trial-info {
            display: flex;
            align-items: center;
            gap: 32px;

            .trial-days {
              display: flex;
              flex-direction: column;
              align-items: center;

              .days-number {
                font-size: 36px;
                font-weight: 600;
                color: #ff9800;
                line-height: 1;
              }

              .days-label {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.6);
                margin-top: 4px;
              }
            }

            .trial-end-date {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.7);
            }
          }
        }
      }
    }
  }
}

// Dark theme support
.dark-theme {
  .tenant-analytics {
    .analytics-overview {
      .overview-card {
        .overview-grid {
          .overview-item {
            .overview-content {
              .overview-label {
                color: rgba(255, 255, 255, 0.6);
              }
            }
          }
        }
      }

      .activity-card {
        .activity-metrics {
          .metric-item {
            background-color: rgba(255, 255, 255, 0.05);

            .metric-label {
              color: rgba(255, 255, 255, 0.6);
            }
          }
        }
      }
    }

    .analytics-tabs {
      .tab-content {
        .stats-grid {
          .stat-card {
            .stat-item {
              .stat-details {
                .stat-label {
                  color: rgba(255, 255, 255, 0.6);
                }
              }
            }
          }
        }

        .usage-cards {
          .usage-card {
            .usage-info {
              .usage-percentage {
                color: rgba(255, 255, 255, 0.6);
              }
            }
          }

          .trial-card {
            .trial-info {
              .trial-days {
                .days-label {
                  color: rgba(255, 255, 255, 0.6);
                }
              }

              .trial-end-date {
                color: rgba(255, 255, 255, 0.7);
              }
            }
          }
        }
      }
    }
  }
}
