import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { TenantStats } from '../../../services/tenant-management.service';

@Component({
  selector: 'app-tenant-analytics',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatProgressBarModule,
    MatChipsModule,
    MatButtonModule,
    MatTabsModule,
    TranslateModule
  ],
  templateUrl: './tenant-analytics.component.html',
  styleUrls: ['./tenant-analytics.component.scss']
})
export class TenantAnalyticsComponent implements OnInit {
  @Input() tenantStats: TenantStats | null = null;
  @Input() tenantId: string = '';

  constructor() {}

  ngOnInit(): void {}

  getStorageUsagePercentage(): number {
    if (!this.tenantStats?.maxStorageMB || this.tenantStats.maxStorageMB === 0) {
      return 0;
    }
    return (this.tenantStats.storageUsedMB / this.tenantStats.maxStorageMB) * 100;
  }

  getStudentUsagePercentage(): number {
    if (!this.tenantStats?.maxStudents || this.tenantStats.maxStudents === 0) {
      return 0;
    }
    return (this.tenantStats.totalStudents / this.tenantStats.maxStudents) * 100;
  }

  getFacultyUsagePercentage(): number {
    if (!this.tenantStats?.maxFaculty || this.tenantStats.maxFaculty === 0) {
      return 0;
    }
    return (this.tenantStats.totalFaculty / this.tenantStats.maxFaculty) * 100;
  }

  getTrialDaysRemaining(): number {
    if (!this.tenantStats?.trialEndDate) {
      return 0;
    }
    const endDate = new Date(this.tenantStats.trialEndDate);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  getUsageColor(percentage: number): string {
    if (percentage >= 90) return 'warn';
    if (percentage >= 75) return 'accent';
    return 'primary';
  }

  formatStorageSize(sizeInMB: number): string {
    if (sizeInMB >= 1024) {
      return `${(sizeInMB / 1024).toFixed(1)} GB`;
    }
    return `${sizeInMB} MB`;
  }

  getActivityTrend(): 'up' | 'down' | 'stable' {
    // Simple logic - in real implementation, you'd compare with previous period
    if (!this.tenantStats) return 'stable';
    
    const activityRatio = this.tenantStats.activeStudentsThisMonth / this.tenantStats.totalStudents;
    if (activityRatio > 0.8) return 'up';
    if (activityRatio < 0.5) return 'down';
    return 'stable';
  }

  getTrendIcon(): string {
    const trend = this.getActivityTrend();
    switch (trend) {
      case 'up': return 'trending_up';
      case 'down': return 'trending_down';
      default: return 'trending_flat';
    }
  }

  getTrendColor(): string {
    const trend = this.getActivityTrend();
    switch (trend) {
      case 'up': return 'primary';
      case 'down': return 'warn';
      default: return 'accent';
    }
  }
}
