<div class="tenant-detail-container" *ngIf="tenant">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <button mat-icon-button (click)="goBack()" class="back-button">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <div class="tenant-info">
        <div class="tenant-icon">
          <mat-icon>{{ getTypeIcon(tenant.type) }}</mat-icon>
        </div>
        <div class="tenant-details">
          <h2 class="tenant-name">{{ tenant.displayName || tenant.name }}</h2>
          <p class="tenant-meta">
            <span class="tenant-slug">{{ tenant.slug }}</span>
            <mat-chip [color]="getStatusColor(tenant.status)">
              {{ tenant.isActive ? 'Active' : 'Inactive' }}
            </mat-chip>
          </p>
        </div>
      </div>
    </div>
    <div class="header-actions">
      <button mat-stroked-button (click)="editTenant()">
        <mat-icon>edit</mat-icon>
        {{ 'admin.common.edit' | translate }}
      </button>
      <button mat-stroked-button 
              [color]="tenant.isActive ? 'warn' : 'primary'"
              (click)="toggleTenantStatus()">
        <mat-icon>{{ tenant.isActive ? 'block' : 'check_circle' }}</mat-icon>
        {{ tenant.isActive ? ('admin.common.deactivate' | translate) : ('admin.common.activate' | translate) }}
      </button>
      <button mat-raised-button color="primary" (click)="exportData()">
        <mat-icon>download</mat-icon>
        {{ 'admin.common.export' | translate }}
      </button>
    </div>
  </div>

  <!-- Content Tabs -->
  <mat-tab-group class="tenant-tabs">
    <!-- Overview Tab -->
    <mat-tab label="Overview">
      <div class="tab-content">
        <div class="overview-grid">
          <!-- Basic Information -->
          <mat-card class="info-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>info</mat-icon>
                Basic Information
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="info-row">
                <span class="label">Name:</span>
                <span class="value">{{ tenant.name }}</span>
              </div>
              <div class="info-row">
                <span class="label">Display Name:</span>
                <span class="value">{{ tenant.displayName }}</span>
              </div>
              <div class="info-row">
                <span class="label">Type:</span>
                <span class="value">{{ tenant.type }}</span>
              </div>
              <div class="info-row">
                <span class="label">Slug:</span>
                <span class="value">{{ tenant.slug }}</span>
              </div>
              <div class="info-row" *ngIf="tenant.customDomain">
                <span class="label">Custom Domain:</span>
                <span class="value">{{ tenant.customDomain }}</span>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Localization Settings -->
          <mat-card class="info-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>language</mat-icon>
                Localization
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="info-row">
                <span class="label">Language:</span>
                <span class="value">{{ tenant.defaultLanguage }}</span>
              </div>
              <div class="info-row">
                <span class="label">Time Zone:</span>
                <span class="value">{{ tenant.timeZone }}</span>
              </div>
              <div class="info-row">
                <span class="label">Currency:</span>
                <span class="value">{{ tenant.currency }}</span>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Trial Information -->
          <mat-card class="info-card" *ngIf="tenant.isTrialActive">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>schedule</mat-icon>
                Trial Information
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="info-row">
                <span class="label">Trial Status:</span>
                <mat-chip color="accent">Active</mat-chip>
              </div>
              <div class="info-row" *ngIf="tenant.trialEndDate">
                <span class="label">Trial Ends:</span>
                <span class="value">{{ tenant.trialEndDate | date:'medium' }}</span>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Statistics -->
          <mat-card class="info-card" *ngIf="tenantStats">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>analytics</mat-icon>
                Statistics
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ tenantStats.totalUsers || 0 }}</div>
                  <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ tenantStats.totalStudents || 0 }}</div>
                  <div class="stat-label">Students</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ tenantStats.totalFaculty || 0 }}</div>
                  <div class="stat-label">Faculty</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ tenantStats.totalParents || 0 }}</div>
                  <div class="stat-label">Parents</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ tenantStats.totalGrades || 0 }}</div>
                  <div class="stat-label">Grades</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ tenantStats.totalSections || 0 }}</div>
                  <div class="stat-label">Sections</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ tenantStats.totalSubjects || 0 }}</div>
                  <div class="stat-label">Subjects</div>
                </div>
                <div class="stat-item" *ngIf="tenantStats.maxStorageMB">
                  <div class="stat-value">{{ (tenantStats.storageUsedMB / 1024) | number:'1.1-1' }}GB</div>
                  <div class="stat-label">Storage Used</div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <!-- Users Tab -->
    <mat-tab label="Users">
      <div class="tab-content">
        <div class="users-section">
          <div class="section-header">
            <h3>User Access Management</h3>
            <button mat-raised-button color="primary" (click)="grantAccess()">
              <mat-icon>person_add</mat-icon>
              Grant Access
            </button>
          </div>

          <mat-card class="users-table-card">
            <mat-card-content>
              <table mat-table [dataSource]="tenantUsers" class="users-table">
                <!-- User Column -->
                <ng-container matColumnDef="user">
                  <th mat-header-cell *matHeaderCellDef>User</th>
                  <td mat-cell *matCellDef="let user">
                    <div class="user-info">
                      <div class="user-details">
                        <div class="user-name">{{ user.userName }}</div>
                        <div class="user-email">{{ user.userEmail }}</div>
                      </div>
                    </div>
                  </td>
                </ng-container>

                <!-- Role Column -->
                <ng-container matColumnDef="role">
                  <th mat-header-cell *matHeaderCellDef>Role</th>
                  <td mat-cell *matCellDef="let user">
                    <mat-chip [color]="getRoleColor(user.role)">{{ user.role }}</mat-chip>
                  </td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let user">
                    <mat-chip [color]="user.isActive ? 'primary' : 'warn'">
                      {{ user.isActive ? 'Active' : 'Inactive' }}
                    </mat-chip>
                  </td>
                </ng-container>

                <!-- Granted At Column -->
                <ng-container matColumnDef="grantedAt">
                  <th mat-header-cell *matHeaderCellDef>Granted</th>
                  <td mat-cell *matCellDef="let user">
                    <div class="granted-info">
                      <div class="granted-date">{{ user.grantedAt | date:'short' }}</div>
                      <div class="granted-by">by {{ user.grantedBy }}</div>
                    </div>
                  </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let user">
                    <button mat-icon-button 
                            matTooltip="Revoke Access" 
                            (click)="revokeAccess(user.userId)"
                            color="warn">
                      <mat-icon>person_remove</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="userColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: userColumns;"></tr>
              </table>

              <!-- Empty State -->
              <div *ngIf="tenantUsers.length === 0" class="empty-state">
                <mat-icon>people</mat-icon>
                <h3>No Users Found</h3>
                <p>No users have been granted access to this tenant yet.</p>
                <button mat-raised-button color="primary" (click)="grantAccess()">
                  <mat-icon>person_add</mat-icon>
                  Grant First Access
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <!-- Analytics Tab -->
    <mat-tab label="Analytics">
      <div class="tab-content">
        <app-tenant-analytics
          [tenantStats]="tenantStats"
          [tenantId]="tenantId">
        </app-tenant-analytics>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>

<!-- Loading State -->
<div *ngIf="loading" class="loading-container">
  <mat-spinner></mat-spinner>
  <p>Loading tenant details...</p>
</div>
