import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { TenantManagementService, TenantInfo, TenantUserAccess, TenantStats } from '../../../services/tenant-management.service';
import { TenantAnalyticsComponent } from '../tenant-analytics/tenant-analytics.component';

@Component({
  selector: 'app-tenant-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatTableModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    TranslateModule,
    TenantAnalyticsComponent
  ],
  templateUrl: './tenant-detail.component.html',
  styleUrls: ['./tenant-detail.component.scss']
})
export class TenantDetailComponent implements OnInit {
  tenant: TenantInfo | null = null;
  tenantUsers: TenantUserAccess[] = [];
  tenantStats: TenantStats | null = null;
  loading = false;
  tenantId: string;

  userColumns: string[] = ['user', 'role', 'status', 'grantedAt', 'actions'];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private tenantService: TenantManagementService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.tenantId = this.route.snapshot.params['id'];
  }

  ngOnInit() {
    this.loadTenantDetails();
    this.loadTenantUsers();
    this.loadTenantStats();
  }

  loadTenantDetails() {
    this.loading = true;
    this.tenantService.getTenantById(this.tenantId).subscribe({
      next: (tenant) => {
        this.tenant = tenant;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading tenant details:', error);
        this.snackBar.open('Error loading tenant details', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  loadTenantUsers() {
    this.tenantService.getTenantUsers(this.tenantId).subscribe({
      next: (users) => {
        this.tenantUsers = users;
      },
      error: (error) => {
        console.error('Error loading tenant users:', error);
      }
    });
  }

  loadTenantStats() {
    this.tenantService.getTenantStats(this.tenantId).subscribe({
      next: (stats) => {
        this.tenantStats = stats;
      },
      error: (error) => {
        console.error('Error loading tenant stats:', error);
      }
    });
  }

  editTenant() {
    this.router.navigate(['/admin/tenants/edit', this.tenantId]);
  }

  toggleTenantStatus() {
    if (!this.tenant) return;

    const newStatus = !this.tenant.isActive;
    this.tenantService.updateTenantStatus(this.tenantId, newStatus).subscribe({
      next: () => {
        this.tenant!.isActive = newStatus;
        this.snackBar.open(
          `Tenant ${newStatus ? 'activated' : 'deactivated'} successfully`,
          'Close',
          { duration: 3000 }
        );
      },
      error: (error) => {
        console.error('Error updating tenant status:', error);
        this.snackBar.open('Error updating tenant status', 'Close', { duration: 3000 });
      }
    });
  }

  grantAccess() {
    // Open dialog to grant user access
  }

  revokeAccess(userId: string) {
    this.tenantService.revokeTenantAccess(this.tenantId, userId).subscribe({
      next: () => {
        this.tenantUsers = this.tenantUsers.filter(u => u.userId !== userId);
        this.snackBar.open('Access revoked successfully', 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error('Error revoking access:', error);
        this.snackBar.open('Error revoking access', 'Close', { duration: 3000 });
      }
    });
  }

  exportData() {
    this.tenantService.exportTenantData(this.tenantId).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `tenant-${this.tenant?.slug}-data.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error exporting data:', error);
        this.snackBar.open('Error exporting data', 'Close', { duration: 3000 });
      }
    });
  }

  goBack() {
    this.router.navigate(['/admin/tenants']);
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'active': return 'primary';
      case 'inactive': return 'warn';
      case 'trial': return 'accent';
      default: return '';
    }
  }

  getTypeIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'school': return 'school';
      case 'university': return 'account_balance';
      case 'institute': return 'business';
      default: return 'business';
    }
  }

  getRoleColor(role: string): string {
    switch (role.toLowerCase()) {
      case 'admin': return 'primary';
      case 'user': return 'accent';
      default: return '';
    }
  }
}
