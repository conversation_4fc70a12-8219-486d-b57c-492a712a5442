import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface TenantInfo {
  id: string;
  name: string;
  slug: string;
  displayName: string;
  type: string;
  status: string;
  isActive: boolean;
  isTrialActive: boolean;
  trialEndDate?: string;
  customDomain?: string;
  defaultLanguage: string;
  timeZone: string;
  currency: string;
  createdAt: string;
  userCount?: number;
  subscriptionStatus?: string;
}

export interface CreateTenantRequest {
  name: string;
  slug: string;
  displayName: string;
  type: string;
  customDomain?: string;
  defaultLanguage: string;
  timeZone: string;
  currency: string;
  isTrialActive: boolean;
  trialEndDate?: string;
  adminUser?: {
    firstName: string;
    lastName: string;
    email: string;
    username: string;
    password: string;
  };
}

export interface TenantUserAccess {
  userId: string;
  userEmail: string;
  userName: string;
  role: string;
  isActive: boolean;
  grantedAt: string;
  grantedBy: string;
}

export interface GrantTenantAccessRequest {
  userEmail: string;
  role: 'Admin' | 'User';
  sendInvitation: boolean;
}

export interface TenantStats {
  tenantId: string;
  tenantName: string;

  // User statistics
  totalUsers: number;
  totalStudents: number;
  totalFaculty: number;
  totalParents: number;

  // Academic structure statistics
  totalGrades: number;
  totalSections: number;
  totalSubjects: number;
  totalAcademicYears: number;
  totalTerms: number;

  // Content statistics
  totalNotices: number;
  totalEvents: number;

  // Activity metrics
  activeStudentsThisMonth: number;
  newStudentsThisMonth: number;

  // Trial and subscription info
  isTrialActive: boolean;
  trialEndDate?: string;
  joinedDate: string;
  lastActivityDate: string;

  // Storage and limits
  storageUsedMB: number;
  maxStorageMB?: number;
  maxStudents?: number;
  maxFaculty?: number;
}

@Injectable({
  providedIn: 'root'
})
export class TenantManagementService {
  private readonly apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Get all tenants (System Admin only)
   */
  getAllTenants(): Observable<TenantInfo[]> {
    return this.http.get<any>(`${this.apiUrl}/tenant/list`).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Get tenant by ID
   */
  getTenantById(tenantId: string): Observable<TenantInfo> {
    return this.http.get<any>(`${this.apiUrl}/tenant/${tenantId}`).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Create new tenant with admin user
   */
  createTenant(tenantData: CreateTenantRequest): Observable<TenantInfo> {
    return this.http.post<any>(`${this.apiUrl}/tenant/create`, tenantData).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Update existing tenant
   */
  updateTenant(tenantId: string, tenantData: Partial<CreateTenantRequest>): Observable<TenantInfo> {
    return this.http.put<any>(`${this.apiUrl}/tenant/${tenantId}`, tenantData).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Update tenant status (activate/deactivate)
   */
  updateTenantStatus(tenantId: string, isActive: boolean): Observable<void> {
    return this.http.patch<any>(`${this.apiUrl}/tenant/${tenantId}/status`, { isActive }).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Delete tenant (soft delete)
   */
  deleteTenant(tenantId: string): Observable<void> {
    return this.http.delete<any>(`${this.apiUrl}/tenant/${tenantId}`).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Get users with access to a specific tenant
   */
  getTenantUsers(tenantId: string): Observable<TenantUserAccess[]> {
    return this.http.get<any>(`${this.apiUrl}/tenant/${tenantId}/users`).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Grant user access to tenant
   */
  grantTenantAccess(tenantId: string, accessData: GrantTenantAccessRequest): Observable<void> {
    return this.http.post<any>(`${this.apiUrl}/tenant/${tenantId}/grant-access`, accessData).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Revoke user access from tenant
   */
  revokeTenantAccess(tenantId: string, userId: string): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/tenant/${tenantId}/revoke-access/${userId}`).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Check if tenant slug is available
   */
  checkSlugAvailability(slug: string, excludeTenantId?: string): Observable<boolean> {
    let params = new HttpParams().set('slug', slug);
    if (excludeTenantId) {
      params = params.set('excludeTenantId', excludeTenantId);
    }

    return this.http.get<any>(`${this.apiUrl}/tenant/check-slug`, { params }).pipe(
      map(response => response.data?.available || false)
    );
  }

  /**
   * Get tenant statistics
   */
  getTenantStats(tenantId: string): Observable<TenantStats> {
    return this.http.get<any>(`${this.apiUrl}/tenant/${tenantId}/stats`).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Search tenants with filters
   */
  searchTenants(filters: {
    search?: string;
    status?: string;
    type?: string;
    page?: number;
    limit?: number;
  }): Observable<{ tenants: TenantInfo[]; total: number }> {
    let params = new HttpParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<any>(`${this.apiUrl}/tenant/search`, { params }).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Export tenant data
   */
  exportTenantData(tenantId: string, format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/tenant/${tenantId}/export`, {
      params: { format },
      responseType: 'blob'
    });
  }

  /**
   * Get tenant setup status
   */
  getTenantSetupStatus(tenantId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/tenant-setup/${tenantId}/status`).pipe(
      map(response => response.data || response)
    );
  }
}
