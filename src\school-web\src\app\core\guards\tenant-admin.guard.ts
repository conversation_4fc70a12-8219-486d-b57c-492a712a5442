import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { map, catchError, of } from 'rxjs';
import { TenantService } from '../services/tenant.service';
import { AuthService } from '../services/auth.service';

/**
 * Guard to ensure only tenant admins can access tenant setup and management features
 * Non-admin users are redirected to a pending page where they wait for admin setup
 */
export const tenantAdminGuard: CanActivateFn = (route, state) => {
  const tenantService = inject(TenantService);
  const authService = inject(AuthService);
  const router = inject(Router);

  console.log('TenantAdminGuard - Checking admin access for route:', state.url);

  // If user is System Admin, allow access to all tenant management
  if (authService.isSystemAdmin()) {
    console.log('TenantAdminGuard - System Admin detected, allowing access');
    return true;
  }

  // Check if user is authenticated
  if (!authService.isLoggedIn()) {
    console.log('TenantAdminGuard - User not authenticated, redirecting to login');
    router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
    return false;
  }

  // Check if user is a tenant admin
  if (!authService.isTenantAdmin()) {
    console.log('TenantAdminGuard - User is not a tenant admin, redirecting to pending page');
    
    // Check if user has any tenant access
    const currentUser = authService.getCurrentUser();
    const userTenants = currentUser?.tenants || [];
    
    if (userTenants.length === 0) {
      // User has no tenant access, redirect to create tenant
      router.navigate(['/tenant-setup/create'], {
        queryParams: { returnUrl: state.url }
      });
      return false;
    } else {
      // User has tenant access but is not admin, redirect to pending
      router.navigate(['/tenant-setup/pending'], {
        queryParams: { returnUrl: state.url }
      });
      return false;
    }
  }

  // For tenant admins, check if they have a valid tenant context
  return tenantService.getCurrentTenant().pipe(
    map(tenant => {
      if (!tenant) {
        console.log('TenantAdminGuard - No tenant context found for admin');
        
        // Check admin's tenant access
        const currentUser = authService.getCurrentUser();
        const userTenants = currentUser?.tenants || [];
        
        if (userTenants.length === 0) {
          console.log('TenantAdminGuard - Admin has no tenant access, redirecting to create');
          router.navigate(['/tenant-setup/create'], {
            queryParams: { returnUrl: state.url }
          });
          return false;
        } else if (userTenants.length === 1) {
          console.log('TenantAdminGuard - Admin has one tenant, setting context');
          const tenantSlug = userTenants[0].tenantSlug || userTenants[0].tenantId;
          tenantService.setDevelopmentTenant(tenantSlug);
          // Reload to reinitialize with tenant context
          window.location.reload();
          return false;
        } else {
          console.log('TenantAdminGuard - Admin has multiple tenants, redirecting to selection');
          router.navigate(['/tenant-setup/select'], {
            queryParams: { returnUrl: state.url }
          });
          return false;
        }
      }

      // Check if tenant is active
      if (!tenant.isActive) {
        console.log('TenantAdminGuard - Tenant is inactive');
        router.navigate(['/tenant-setup/inactive'], {
          queryParams: { returnUrl: state.url }
        });
        return false;
      }

      // Verify admin has access to this specific tenant
      const userTenants = authService.getCurrentUser()?.tenants || [];
      const hasAdminAccess = userTenants.some((ut: any) => 
        ut.tenantId === tenant.id && ut.isActive && ut.role === 'Admin'
      );
      
      if (!hasAdminAccess) {
        console.log('TenantAdminGuard - Admin does not have admin access to this tenant');
        router.navigate(['/tenant-setup/access-denied'], {
          queryParams: { returnUrl: state.url }
        });
        return false;
      }

      console.log('TenantAdminGuard - Tenant admin access verified, allowing access');
      return true;
    }),
    catchError(error => {
      console.error('TenantAdminGuard - Error checking tenant admin access:', error);
      router.navigate(['/tenant-setup/error'], {
        queryParams: { returnUrl: state.url }
      });
      return of(false);
    })
  );
};
