import { BaseModel } from './base.model';

/**
 * Subject model aligned with SubjectDto from API
 */
export interface Subject extends BaseModel {
  name: string;
  code: string;
  description: string;
  shortDescription: string;
  credits: number;
  hoursPerWeek: number;
  subjectType: SubjectType;
  isCore: boolean;
  isElective: boolean;
  isActive: boolean;
  displayOrder: number;
  gradeId: string;
  gradeName: string;
  departmentId: string;
  departmentName: string;
  academicYearId: string;
  academicYearName: string;
  prerequisites: string;
  learningObjectives: string;
  assessmentCriteria: string;
  textbooks: string;
  resources: string;
  syllabus: string;
  passMarks: number;
  fullMarks: number;
  practicalMarks: number;
  theoryMarks: number;
  internalMarks: number;
  externalMarks: number;
  hasLab: boolean;
  labHours: number;
  hasProject: boolean;
  projectMarks: number;
  hasAssignment: boolean;
  assignmentMarks: number;
  hasQuiz: boolean;
  quizMarks: number;
  hasMidterm: boolean;
  midtermMarks: number;
  hasFinal: boolean;
  finalMarks: number;
  hasOral: boolean;
  oralMarks: number;
  hasPractical: boolean;
  practicalExamMarks: number;
  hasFieldwork: boolean;
  fieldworkMarks: number;
  hasPresentation: boolean;
  presentationMarks: number;
  hasPortfolio: boolean;
  portfolioMarks: number;
  hasAttendance: boolean;
  attendanceMarks: number;
  minimumAttendance: number;
  isOptional: boolean;
  maxStudents: number;
  minStudents: number;
  difficulty: DifficultyLevel;
  language: string;
  medium: string;
  category: string;
  tags: string;
  keywords: string;
  objectives: string;
  outcomes: string;
  methodology: string;
  evaluation: string;
  references: string;
  notes: string;
  remarks: string;
  translations: SubjectTranslation[];
}

/**
 * Subject translation model
 */
export interface SubjectTranslation {
  id: string;
  subjectId: string;
  languageCode: string;
  name: string;
  description: string;
  shortDescription: string;
  prerequisites: string;
  learningObjectives: string;
  assessmentCriteria: string;
  textbooks: string;
  resources: string;
  syllabus: string;
  objectives: string;
  outcomes: string;
  methodology: string;
  evaluation: string;
  references: string;
  notes: string;
  remarks: string;
}

/**
 * Create subject model
 */
export interface CreateSubject {
  name: string;
  code: string;
  description: string;
  shortDescription?: string;
  credits: number;
  hoursPerWeek: number;
  subjectType: SubjectType;
  isCore: boolean;
  isElective: boolean;
  isActive?: boolean;
  displayOrder?: number;
  gradeId: string;
  departmentId: string;
  academicYearId: string;
  prerequisites?: string;
  learningObjectives?: string;
  assessmentCriteria?: string;
  textbooks?: string;
  resources?: string;
  syllabus?: string;
  passMarks?: number;
  fullMarks?: number;
  practicalMarks?: number;
  theoryMarks?: number;
  internalMarks?: number;
  externalMarks?: number;
  hasLab?: boolean;
  labHours?: number;
  hasProject?: boolean;
  projectMarks?: number;
  hasAssignment?: boolean;
  assignmentMarks?: number;
  hasQuiz?: boolean;
  quizMarks?: number;
  hasMidterm?: boolean;
  midtermMarks?: number;
  hasFinal?: boolean;
  finalMarks?: number;
  hasOral?: boolean;
  oralMarks?: number;
  hasPractical?: boolean;
  practicalExamMarks?: number;
  hasFieldwork?: boolean;
  fieldworkMarks?: number;
  hasPresentation?: boolean;
  presentationMarks?: number;
  hasPortfolio?: boolean;
  portfolioMarks?: number;
  hasAttendance?: boolean;
  attendanceMarks?: number;
  minimumAttendance?: number;
  isOptional?: boolean;
  maxStudents?: number;
  minStudents?: number;
  difficulty?: DifficultyLevel;
  language?: string;
  medium?: string;
  category?: string;
  tags?: string;
  keywords?: string;
  objectives?: string;
  outcomes?: string;
  methodology?: string;
  evaluation?: string;
  references?: string;
  notes?: string;
  remarks?: string;
  translations?: CreateSubjectTranslation[];
}

/**
 * Update subject model
 */
export interface UpdateSubject extends Partial<CreateSubject> {
  id: string;
}

/**
 * Create subject translation model
 */
export interface CreateSubjectTranslation {
  languageCode: string;
  name: string;
  description: string;
  shortDescription?: string;
  prerequisites?: string;
  learningObjectives?: string;
  assessmentCriteria?: string;
  textbooks?: string;
  resources?: string;
  syllabus?: string;
  objectives?: string;
  outcomes?: string;
  methodology?: string;
  evaluation?: string;
  references?: string;
  notes?: string;
  remarks?: string;
}

/**
 * Subject filter model
 */
export interface SubjectFilter {
  page?: number;
  pageSize?: number;
  searchTerm?: string;
  gradeId?: string;
  departmentId?: string;
  academicYearId?: string;
  subjectType?: SubjectType;
  isCore?: boolean;
  isElective?: boolean;
  isActive?: boolean;
  difficulty?: DifficultyLevel;
  hasLab?: boolean;
  isOptional?: boolean;
  sortBy?: string;
  sortDescending?: boolean;
}

/**
 * Subject statistics model
 */
export interface SubjectStatistics {
  totalSubjects: number;
  activeSubjects: number;
  inactiveSubjects: number;
  coreSubjects: number;
  electiveSubjects: number;
  subjectsByType: { [key: string]: number };
  subjectsByGrade: { [key: string]: number };
  subjectsByDepartment: { [key: string]: number };
  averageCredits: number;
  averageHoursPerWeek: number;
}

/**
 * Subject enums
 */
export enum SubjectType {
  Core = 'Core',
  Elective = 'Elective',
  Optional = 'Optional',
  Mandatory = 'Mandatory',
  Practical = 'Practical',
  Theory = 'Theory',
  Lab = 'Lab',
  Project = 'Project',
  Seminar = 'Seminar',
  Workshop = 'Workshop',
  Internship = 'Internship',
  Research = 'Research'
}

export enum DifficultyLevel {
  Beginner = 'Beginner',
  Intermediate = 'Intermediate',
  Advanced = 'Advanced',
  Expert = 'Expert'
}
