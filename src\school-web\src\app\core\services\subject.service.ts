import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseApiService } from './base-api.service';
import { ErrorHandlerService } from './error-handler.service';
import { ApiResponse } from '../models/api-response.model';
import {
  Subject,
  CreateSubject,
  UpdateSubject,
  SubjectFilter,
  SubjectStatistics,
  CreateSubjectTranslation,
  SubjectTranslation
} from '../models/subject.model';

export interface SubjectListResponse {
  data: Subject[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class SubjectService extends BaseApiService {
  private readonly subjectApiUrl = `${this.apiUrl}/api/subjects`;

  constructor(
    protected override http: HttpClient,
    protected override errorHandler: ErrorHandlerService
  ) {
    super(http, errorHandler);
  }

  /**
   * Get all subjects with filtering and pagination
   */
  getSubjects(filter: SubjectFilter = {}): Observable<SubjectListResponse> {
    let params = new HttpParams();

    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.searchTerm) params = params.set('searchTerm', filter.searchTerm);
    if (filter.gradeId) params = params.set('gradeId', filter.gradeId);
    if (filter.departmentId) params = params.set('departmentId', filter.departmentId);
    if (filter.academicYearId) params = params.set('academicYearId', filter.academicYearId);
    if (filter.subjectType) params = params.set('subjectType', filter.subjectType);
    if (filter.isCore !== undefined) params = params.set('isCore', filter.isCore.toString());
    if (filter.isElective !== undefined) params = params.set('isElective', filter.isElective.toString());
    if (filter.isActive !== undefined) params = params.set('isActive', filter.isActive.toString());
    if (filter.difficulty) params = params.set('difficulty', filter.difficulty);
    if (filter.hasLab !== undefined) params = params.set('hasLab', filter.hasLab.toString());
    if (filter.isOptional !== undefined) params = params.set('isOptional', filter.isOptional.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDescending !== undefined) params = params.set('sortDescending', filter.sortDescending.toString());

    return this.http.get<SubjectListResponse>(this.subjectApiUrl, { params });
  }

  /**
   * Get subject by ID
   */
  getSubject(id: string): Observable<Subject> {
    return this.http.get<ApiResponse<Subject>>(`${this.subjectApiUrl}/${id}`)
      .pipe(map(response => response.data));
  }

  /**
   * Create new subject
   */
  createSubject(subject: CreateSubject): Observable<string> {
    return this.http.post<ApiResponse<string>>(this.subjectApiUrl, subject)
      .pipe(map(response => response.data));
  }

  /**
   * Update existing subject
   */
  updateSubject(id: string, subject: UpdateSubject): Observable<void> {
    return this.http.put<ApiResponse<void>>(`${this.subjectApiUrl}/${id}`, subject)
      .pipe(map(response => response.data));
  }

  /**
   * Delete subject
   */
  deleteSubject(id: string): Observable<void> {
    return this.http.delete<ApiResponse<void>>(`${this.subjectApiUrl}/${id}`)
      .pipe(map(response => response.data));
  }

  /**
   * Activate subject
   */
  activateSubject(id: string): Observable<void> {
    return this.http.patch<ApiResponse<void>>(`${this.subjectApiUrl}/${id}/activate`, {})
      .pipe(map(response => response.data));
  }

  /**
   * Deactivate subject
   */
  deactivateSubject(id: string): Observable<void> {
    return this.http.patch<ApiResponse<void>>(`${this.subjectApiUrl}/${id}/deactivate`, {})
      .pipe(map(response => response.data));
  }

  /**
   * Validate subject code
   */
  validateSubjectCode(code: string, gradeId?: string, excludeId?: string): Observable<boolean> {
    let params = new HttpParams().set('code', code);
    if (gradeId) params = params.set('gradeId', gradeId);
    if (excludeId) params = params.set('excludeId', excludeId);

    return this.http.get<ApiResponse<boolean>>(`${this.subjectApiUrl}/validate/code`, { params })
      .pipe(map(response => response.data));
  }

  /**
   * Get subject statistics
   */
  getSubjectStatistics(academicYearId?: string): Observable<SubjectStatistics> {
    let params = new HttpParams();
    if (academicYearId) params = params.set('academicYearId', academicYearId);

    return this.http.get<ApiResponse<SubjectStatistics>>(`${this.subjectApiUrl}/statistics`, { params })
      .pipe(map(response => response.data));
  }

  /**
   * Get subjects by grade
   */
  getSubjectsByGrade(gradeId: string): Observable<Subject[]> {
    return this.http.get<ApiResponse<Subject[]>>(`${this.subjectApiUrl}/grade/${gradeId}`)
      .pipe(map(response => response.data));
  }

  /**
   * Get subjects by department
   */
  getSubjectsByDepartment(departmentId: string): Observable<Subject[]> {
    return this.http.get<ApiResponse<Subject[]>>(`${this.subjectApiUrl}/department/${departmentId}`)
      .pipe(map(response => response.data));
  }

  /**
   * Bulk create subjects
   */
  bulkCreateSubjects(subjects: CreateSubject[]): Observable<void> {
    return this.http.post<ApiResponse<void>>(`${this.subjectApiUrl}/bulk/create`, subjects)
      .pipe(map(response => response.data));
  }

  /**
   * Bulk update subjects
   */
  bulkUpdateSubjects(updates: { id: string; updates: Partial<UpdateSubject> }[]): Observable<void> {
    return this.http.put<ApiResponse<void>>(`${this.subjectApiUrl}/bulk/update`, updates)
      .pipe(map(response => response.data));
  }

  /**
   * Bulk delete subjects
   */
  bulkDeleteSubjects(ids: string[]): Observable<void> {
    return this.http.delete<ApiResponse<void>>(`${this.subjectApiUrl}/bulk/delete`, { body: ids })
      .pipe(map(response => response.data));
  }

  /**
   * Export subjects
   */
  exportSubjects(filter: SubjectFilter = {}): Observable<Blob> {
    let params = new HttpParams();
    if (filter.gradeId) params = params.set('gradeId', filter.gradeId);
    if (filter.departmentId) params = params.set('departmentId', filter.departmentId);
    if (filter.academicYearId) params = params.set('academicYearId', filter.academicYearId);

    return this.http.get(`${this.subjectApiUrl}/export`, {
      params,
      responseType: 'blob'
    });
  }

  /**
   * Import subjects
   */
  importSubjects(file: File): Observable<void> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<ApiResponse<void>>(`${this.subjectApiUrl}/import`, formData)
      .pipe(map(response => response.data));
  }

  // Translation methods

  /**
   * Get subject translations
   */
  getSubjectTranslations(subjectId: string): Observable<SubjectTranslation[]> {
    return this.http.get<ApiResponse<SubjectTranslation[]>>(`${this.subjectApiUrl}/${subjectId}/translations`)
      .pipe(map(response => response.data));
  }

  /**
   * Add subject translation
   */
  addSubjectTranslation(subjectId: string, translation: CreateSubjectTranslation): Observable<void> {
    return this.http.post<ApiResponse<void>>(`${this.subjectApiUrl}/${subjectId}/translations`, translation)
      .pipe(map(response => response.data));
  }

  /**
   * Update subject translation
   */
  updateSubjectTranslation(subjectId: string, languageCode: string, translation: Partial<CreateSubjectTranslation>): Observable<void> {
    return this.http.put<ApiResponse<void>>(`${this.subjectApiUrl}/${subjectId}/translations/${languageCode}`, translation)
      .pipe(map(response => response.data));
  }

  /**
   * Delete subject translation
   */
  deleteSubjectTranslation(subjectId: string, languageCode: string): Observable<void> {
    return this.http.delete<ApiResponse<void>>(`${this.subjectApiUrl}/${subjectId}/translations/${languageCode}`)
      .pipe(map(response => response.data));
  }
}
