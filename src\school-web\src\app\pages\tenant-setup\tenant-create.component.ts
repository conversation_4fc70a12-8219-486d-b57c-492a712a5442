import { Component, OnIni<PERSON>, On<PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';
import { TenantManagementService, CreateTenantRequest } from '../../admin/services/tenant-management.service';

@Component({
  selector: 'app-tenant-create',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-create-container">
      <mat-card class="create-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>add_business</mat-icon>
            Create Your School
          </mat-card-title>
          <mat-card-subtitle>
            Set up your school management system
          </mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <form [formGroup]="createForm" (ngSubmit)="onSubmit()">
            <div class="form-section">
              <h3>School Information</h3>
              
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>School Name</mat-label>
                <input matInput formControlName="schoolName" placeholder="Enter your school name">
                <mat-error *ngIf="createForm.get('schoolName')?.hasError('required')">
                  School name is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>School Identifier</mat-label>
                <input matInput formControlName="slug" placeholder="e.g., my-school (used in URL)">
                <mat-hint>This will be used in your school's URL: {{slug}}.edumanage.com</mat-hint>
                <mat-error *ngIf="createForm.get('slug')?.hasError('required')">
                  School identifier is required
                </mat-error>
                <mat-error *ngIf="createForm.get('slug')?.hasError('pattern')">
                  Only lowercase letters, numbers, and hyphens allowed
                </mat-error>
              </mat-form-field>

              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>School Type</mat-label>
                  <mat-select formControlName="schoolType">
                    <mat-option value="PrimarySchool">Primary School</mat-option>
                    <mat-option value="SecondarySchool">Secondary School</mat-option>
                    <mat-option value="School">Combined School (K-12)</mat-option>
                    <mat-option value="College">College</mat-option>
                    <mat-option value="University">University</mat-option>
                    <mat-option value="TechnicalInstitute">Technical Institute</mat-option>
                    <mat-option value="Academy">Academy</mat-option>
                    <mat-option value="InternationalSchool">International School</mat-option>
                    <mat-option value="OnlineSchool">Online School</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Country</mat-label>
                  <mat-select formControlName="country">
                    <mat-option value="BD">Bangladesh</mat-option>
                    <mat-option value="IN">India</mat-option>
                    <mat-option value="US">United States</mat-option>
                    <mat-option value="UK">United Kingdom</mat-option>
                    <mat-option value="CA">Canada</mat-option>
                    <mat-option value="AU">Australia</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Contact Email</mat-label>
                <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                <mat-error *ngIf="createForm.get('email')?.hasError('required')">
                  Email is required
                </mat-error>
                <mat-error *ngIf="createForm.get('email')?.hasError('email')">
                  Please enter a valid email
                </mat-error>
              </mat-form-field>

              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Display Name (Optional)</mat-label>
                  <input matInput formControlName="displayName" placeholder="e.g., ABC International School">
                  <mat-hint>If different from school name</mat-hint>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Custom Domain (Optional)</mat-label>
                  <input matInput formControlName="customDomain" placeholder="e.g., school.edu">
                  <mat-error *ngIf="createForm.get('customDomain')?.hasError('pattern')">
                    Please enter a valid domain name
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Language</mat-label>
                  <mat-select formControlName="language">
                    <mat-option value="en-US">English (US)</mat-option>
                    <mat-option value="bn-BD">Bengali (Bangladesh)</mat-option>
                    <mat-option value="hi-IN">Hindi (India)</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Time Zone</mat-label>
                  <mat-select formControlName="timeZone">
                    <mat-option value="Asia/Dhaka">Asia/Dhaka (GMT+6)</mat-option>
                    <mat-option value="Asia/Kolkata">Asia/Kolkata (GMT+5:30)</mat-option>
                    <mat-option value="America/New_York">America/New_York (GMT-5)</mat-option>
                    <mat-option value="Europe/London">Europe/London (GMT+0)</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Currency</mat-label>
                <mat-select formControlName="currency">
                  <mat-option value="USD">USD - US Dollar</mat-option>
                  <mat-option value="BDT">BDT - Bangladeshi Taka</mat-option>
                  <mat-option value="INR">INR - Indian Rupee</mat-option>
                  <mat-option value="EUR">EUR - Euro</mat-option>
                  <mat-option value="GBP">GBP - British Pound</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="form-section">
              <h3>Administrator Account</h3>
              <p class="section-description">Create the primary administrator account for your school</p>

              <div class="form-row">
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>First Name</mat-label>
                  <input matInput formControlName="adminFirstName" placeholder="Enter first name">
                  <mat-error *ngIf="createForm.get('adminFirstName')?.hasError('required')">
                    First name is required
                  </mat-error>
                  <mat-error *ngIf="createForm.get('adminFirstName')?.hasError('minlength')">
                    First name must be at least 2 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Last Name</mat-label>
                  <input matInput formControlName="adminLastName" placeholder="Enter last name">
                  <mat-error *ngIf="createForm.get('adminLastName')?.hasError('required')">
                    Last name is required
                  </mat-error>
                  <mat-error *ngIf="createForm.get('adminLastName')?.hasError('minlength')">
                    Last name must be at least 2 characters
                  </mat-error>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Admin Email</mat-label>
                <input matInput type="email" formControlName="adminEmail" placeholder="<EMAIL>">
                <mat-error *ngIf="createForm.get('adminEmail')?.hasError('required')">
                  Admin email is required
                </mat-error>
                <mat-error *ngIf="createForm.get('adminEmail')?.hasError('email')">
                  Please enter a valid email address
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Username</mat-label>
                <input matInput formControlName="adminUsername" placeholder="Enter username">
                <mat-hint>Username for admin login (3-50 characters, letters, numbers, dots, hyphens, underscores)</mat-hint>
                <mat-error *ngIf="createForm.get('adminUsername')?.hasError('required')">
                  Username is required
                </mat-error>
                <mat-error *ngIf="createForm.get('adminUsername')?.hasError('minlength')">
                  Username must be at least 3 characters
                </mat-error>
                <mat-error *ngIf="createForm.get('adminUsername')?.hasError('pattern')">
                  Username can only contain letters, numbers, dots, hyphens, and underscores
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Password</mat-label>
                <input matInput
                       [type]="hidePassword ? 'password' : 'text'"
                       formControlName="adminPassword"
                       placeholder="Enter secure password">
                <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                  <mat-icon>{{hidePassword ? 'visibility' : 'visibility_off'}}</mat-icon>
                </button>
                <mat-hint>Password must be at least 8 characters with uppercase, lowercase, number, and special character</mat-hint>
                <mat-error *ngIf="createForm.get('adminPassword')?.hasError('required')">
                  Password is required
                </mat-error>
                <mat-error *ngIf="createForm.get('adminPassword')?.hasError('minlength')">
                  Password must be at least 8 characters
                </mat-error>
                <mat-error *ngIf="createForm.get('adminPassword')?.hasError('pattern')">
                  Password must contain uppercase, lowercase, number, and special character
                </mat-error>
              </mat-form-field>

              <!-- Password Strength Indicator -->
              <div class="password-strength" *ngIf="createForm.get('adminPassword')?.value">
                <div class="strength-label">Password Requirements:</div>
                <div class="strength-requirements">
                  <div class="requirement"
                       [class.met]="hasMinLength(createForm.get('adminPassword')?.value || '')">
                    <mat-icon>{{hasMinLength(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                    At least 8 characters
                  </div>
                  <div class="requirement"
                       [class.met]="hasLowercase(createForm.get('adminPassword')?.value || '')">
                    <mat-icon>{{hasLowercase(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                    One lowercase letter
                  </div>
                  <div class="requirement"
                       [class.met]="hasUppercase(createForm.get('adminPassword')?.value || '')">
                    <mat-icon>{{hasUppercase(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                    One uppercase letter
                  </div>
                  <div class="requirement"
                       [class.met]="hasNumber(createForm.get('adminPassword')?.value || '')">
                    <mat-icon>{{hasNumber(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                    One number
                  </div>
                  <div class="requirement"
                       [class.met]="hasSpecialChar(createForm.get('adminPassword')?.value || '')">
                    <mat-icon>{{hasSpecialChar(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                    One special character (&#64;$!%*?&amp;)
                  </div>
                </div>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Confirm Password</mat-label>
                <input matInput
                       [type]="hidePasswordConfirm ? 'password' : 'text'"
                       formControlName="adminPasswordConfirm"
                       placeholder="Confirm your password">
                <button mat-icon-button matSuffix (click)="hidePasswordConfirm = !hidePasswordConfirm" type="button">
                  <mat-icon>{{hidePasswordConfirm ? 'visibility' : 'visibility_off'}}</mat-icon>
                </button>
                <mat-error *ngIf="createForm.get('adminPasswordConfirm')?.hasError('required')">
                  Please confirm your password
                </mat-error>
                <mat-error *ngIf="createForm.hasError('passwordMismatch') && createForm.get('adminPasswordConfirm')?.touched">
                  Passwords do not match
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-section">
              <h3>Subscription Plan</h3>
              <div class="plan-selection">
                <div class="plan-option" 
                     [class.selected]="createForm.get('plan')?.value === 'trial'"
                     (click)="selectPlan('trial')">
                  <h4>Free Trial</h4>
                  <p>30 days free trial</p>
                  <ul>
                    <li>Up to 100 students</li>
                    <li>Basic features</li>
                    <li>Email support</li>
                  </ul>
                </div>
                
                <div class="plan-option" 
                     [class.selected]="createForm.get('plan')?.value === 'basic'"
                     (click)="selectPlan('basic')">
                  <h4>Basic Plan</h4>
                  <p>$29/month</p>
                  <ul>
                    <li>Up to 500 students</li>
                    <li>All features</li>
                    <li>Priority support</li>
                  </ul>
                </div>
              </div>
            </div>
          </form>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button routerLink="/login">
            <mat-icon>arrow_back</mat-icon>
            Back to Login
          </button>
          <div class="spacer"></div>
          <button mat-raised-button color="primary" 
                  [disabled]="!createForm.valid || isLoading"
                  (click)="onSubmit()">
            <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
            <mat-icon *ngIf="!isLoading">school</mat-icon>
            Create School
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .tenant-create-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
      padding: 20px;
      overflow-y: auto;
    }
    
    .create-card {
      max-width: 600px;
      width: 100%;
    }
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .form-section {
      margin-bottom: 24px;
    }
    
    .form-section h3 {
      margin: 0 0 16px 0;
      color: #333;
      font-weight: 500;
    }
    
    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }
    
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .form-field {
      flex: 1;
    }
    
    .plan-selection {
      display: flex;
      gap: 16px;
    }
    
    .plan-option {
      flex: 1;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .plan-option:hover {
      border-color: #4caf50;
    }
    
    .plan-option.selected {
      border-color: #4caf50;
      background-color: #f1f8e9;
    }
    
    .plan-option h4 {
      margin: 0 0 8px 0;
      color: #333;
    }
    
    .plan-option p {
      margin: 0 0 12px 0;
      font-weight: 500;
      color: #4caf50;
    }
    
    .plan-option ul {
      margin: 0;
      padding-left: 16px;
    }
    
    .plan-option li {
      margin-bottom: 4px;
      font-size: 0.9rem;
    }
    
    mat-card-actions {
      display: flex;
      align-items: center;
      padding: 16px 24px;
    }
    
    .spacer {
      flex: 1;
    }
    
    .button-spinner {
      margin-right: 8px;
    }
    
    .section-description {
      margin: -8px 0 16px 0;
      color: #666;
      font-size: 0.9rem;
    }

    .password-strength {
      margin: 8px 0 16px 0;
      padding: 12px;
      border-radius: 8px;
      background-color: #f5f5f5;
    }

    .strength-label {
      font-size: 0.9rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .strength-requirements {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .requirement {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.85rem;
      color: #666;
      transition: color 0.2s;
    }

    .requirement.met {
      color: #4caf50;
    }

    .requirement mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .requirement.met mat-icon {
      color: #4caf50;
    }

    @media (max-width: 768px) {
      .form-row {
        flex-direction: column;
        gap: 8px;
      }

      .plan-selection {
        flex-direction: column;
      }
    }
  `]
})
export class TenantCreateComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);
  private authService = inject(AuthService);
  private tenantService = inject(TenantManagementService);

  createForm!: FormGroup;
  isLoading = false;
  slug = '';
  hidePassword = true;
  hidePasswordConfirm = true;

  ngOnInit() {
    this.createForm = this.fb.group({
      // School Information
      schoolName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      slug: ['', [Validators.required, Validators.pattern(/^[a-z0-9-]+$/), Validators.minLength(2), Validators.maxLength(50)]],
      displayName: [''],
      schoolType: ['', Validators.required],
      country: ['BD', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      customDomain: ['', [Validators.pattern(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/)]],
      language: ['en-US', Validators.required],
      timeZone: ['Asia/Dhaka', Validators.required],
      currency: ['USD', Validators.required],

      // Admin User Information
      adminFirstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      adminLastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      adminEmail: ['', [Validators.required, Validators.email]],
      adminUsername: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(50), Validators.pattern(/^[a-zA-Z0-9_.-]+$/)]],
      adminPassword: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      ]],
      adminPasswordConfirm: ['', Validators.required],

      // Subscription Plan
      plan: ['trial', Validators.required]
    }, {
      validators: this.passwordMatchValidator
    });

    // Auto-generate slug from school name
    this.createForm.get('schoolName')?.valueChanges.subscribe(name => {
      if (name) {
        const generatedSlug = name.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();
        this.createForm.get('slug')?.setValue(generatedSlug);
        this.slug = generatedSlug;
      }
    });

    // Auto-generate display name from school name if not provided
    this.createForm.get('schoolName')?.valueChanges.subscribe(name => {
      const displayName = this.createForm.get('displayName');
      if (name && !displayName?.value) {
        displayName?.setValue(name);
      }
    });

    // Auto-generate admin username from admin email
    this.createForm.get('adminEmail')?.valueChanges.subscribe(email => {
      const username = this.createForm.get('adminUsername');
      if (email && !username?.value) {
        const generatedUsername = email.split('@')[0].toLowerCase()
          .replace(/[^a-z0-9._-]/g, '');
        username?.setValue(generatedUsername);
      }
    });
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  // Custom validator for password confirmation
  passwordMatchValidator(form: FormGroup) {
    const password = form.get('adminPassword');
    const confirmPassword = form.get('adminPasswordConfirm');

    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  // Helper method to check if password meets requirements
  getPasswordErrors(): string[] {
    const password = this.createForm.get('adminPassword')?.value || '';
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('At least 8 characters');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('One lowercase letter');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('One uppercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('One number');
    }
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('One special character (@$!%*?&)');
    }

    return errors;
  }

  // Check if password is strong enough
  isPasswordStrong(): boolean {
    return this.getPasswordErrors().length === 0;
  }

  // Helper methods for password validation
  hasMinLength(password: string): boolean {
    return password.length >= 8;
  }

  hasLowercase(password: string): boolean {
    return /[a-z]/.test(password);
  }

  hasUppercase(password: string): boolean {
    return /[A-Z]/.test(password);
  }

  hasNumber(password: string): boolean {
    return /\d/.test(password);
  }

  hasSpecialChar(password: string): boolean {
    return /[@$!%*?&]/.test(password);
  }

  selectPlan(plan: string) {
    this.createForm.get('plan')?.setValue(plan);
  }

  onSubmit() {
    if (this.createForm.valid) {
      this.isLoading = true;

      const formValue = this.createForm.value;

      // Prepare tenant creation request
      const tenantRequest: CreateTenantRequest = {
        name: formValue.schoolName,
        slug: formValue.slug,
        displayName: formValue.displayName || formValue.schoolName,
        type: formValue.schoolType || 'School',
        customDomain: formValue.customDomain,
        defaultLanguage: formValue.language || 'en-US',
        timeZone: formValue.timeZone || 'Asia/Dhaka',
        currency: formValue.currency || 'USD',
        isTrialActive: true,
        trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days trial
        adminUser: {
          firstName: formValue.adminFirstName,
          lastName: formValue.adminLastName,
          email: formValue.adminEmail,
          username: formValue.adminUsername || formValue.adminEmail,
          password: formValue.adminPassword
        }
      };

      // Call the tenant creation API
      this.tenantService.createTenant(tenantRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open('School created successfully! Redirecting to setup...', 'Close', { duration: 3000 });

          // Redirect to setup wizard with the new tenant
          this.router.navigate(['/tenant-setup/wizard'], {
            queryParams: { tenant: tenantRequest.slug }
          });
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error creating tenant:', error);

          let errorMessage = 'Failed to create school. Please try again.';
          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
        }
      });
    }
  }
}
