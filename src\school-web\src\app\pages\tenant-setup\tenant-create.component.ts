import { Component, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatStepperModule } from '@angular/material/stepper';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '../../core/services/auth.service';
import { TenantManagementService, CreateTenantRequest } from '../../admin/services/tenant-management.service';

@Component({
  selector: 'app-tenant-create',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatStepperModule,
    MatDividerModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-setup-container">
    <!-- Main Content -->
      <div class="setup-content">
        <div class="setup-wizard">
          <!-- Progress Indicator -->
          <div class="progress-section">
            <div class="progress-header">
              <h2>Create Your School</h2>
              <p>Set up your school management system in just a few steps</p>
            </div>
            <div class="step-indicator">
              <div class="step active" [class.completed]="currentStep > 1">
                <div class="step-circle">
                  <mat-icon *ngIf="currentStep > 1">check</mat-icon>
                  <span *ngIf="currentStep <= 1">1</span>
                </div>
                <div class="step-content">
                  <span class="step-title">School Details</span>
                  <span class="step-description">Basic information</span>
                </div>
              </div>
              <div class="step-connector" [class.active]="currentStep > 1"></div>
              <div class="step" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
                <div class="step-circle">
                  <mat-icon *ngIf="currentStep > 2">check</mat-icon>
                  <span *ngIf="currentStep <= 2">2</span>
                </div>
                <div class="step-content">
                  <span class="step-title">Administrator</span>
                  <span class="step-description">Admin account setup</span>
                </div>
              </div>
              <div class="step-connector" [class.active]="currentStep > 2"></div>
              <div class="step" [class.active]="currentStep >= 3">
                <div class="step-circle">
                  <span>3</span>
                </div>
                <div class="step-content">
                  <span class="step-title">Complete</span>
                  <span class="step-description">Finalize setup</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Content -->
          <div class="form-container">
            <form [formGroup]="createForm" (ngSubmit)="onSubmit()">
              <!-- Step 1: School Information -->
              <div class="step-content" *ngIf="currentStep === 1">
                <div class="step-header">
                  <h3>School Information</h3>
                  <p>Tell us about your educational institution</p>
                </div>
                <div class="form-grid">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>School Name</mat-label>
                    <input matInput formControlName="schoolName" placeholder="Enter your school name">
                    <mat-icon matSuffix>school</mat-icon>
                    <mat-error *ngIf="createForm.get('schoolName')?.hasError('required')">
                      School name is required
                    </mat-error>
                    <mat-error *ngIf="createForm.get('schoolName')?.hasError('minlength')">
                      School name must be at least 2 characters
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>School Identifier</mat-label>
                    <input matInput formControlName="slug" placeholder="e.g., my-school">
                    <mat-icon matSuffix>link</mat-icon>
                    <mat-hint>Used in your school's URL: {{slug || 'your-school'}}.edumanage.com</mat-hint>
                    <mat-error *ngIf="createForm.get('slug')?.hasError('required')">
                      School identifier is required
                    </mat-error>
                    <mat-error *ngIf="createForm.get('slug')?.hasError('pattern')">
                      Only lowercase letters, numbers, and hyphens allowed
                    </mat-error>
                  </mat-form-field>

                  <div class="form-row">
                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>School Type</mat-label>
                      <mat-select formControlName="schoolType">
                        <mat-option value="PrimarySchool">Primary School</mat-option>
                        <mat-option value="SecondarySchool">Secondary School</mat-option>
                        <mat-option value="School">Combined School (K-12)</mat-option>
                        <mat-option value="College">College</mat-option>
                        <mat-option value="University">University</mat-option>
                        <mat-option value="TechnicalInstitute">Technical Institute</mat-option>
                        <mat-option value="Academy">Academy</mat-option>
                        <mat-option value="InternationalSchool">International School</mat-option>
                        <mat-option value="OnlineSchool">Online School</mat-option>
                      </mat-select>
                      <mat-icon matSuffix>category</mat-icon>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>Country</mat-label>
                      <mat-select formControlName="country">
                        <mat-option value="BD">🇧🇩 Bangladesh</mat-option>
                        <mat-option value="IN">🇮🇳 India</mat-option>
                        <mat-option value="US">🇺🇸 United States</mat-option>
                        <mat-option value="UK">🇬🇧 United Kingdom</mat-option>
                        <mat-option value="CA">🇨🇦 Canada</mat-option>
                        <mat-option value="AU">🇦🇺 Australia</mat-option>
                      </mat-select>
                      <mat-icon matSuffix>public</mat-icon>
                    </mat-form-field>
                  </div>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Contact Email</mat-label>
                    <input matInput type="email" formControlName="email" placeholder="<EMAIL>">
                    <mat-icon matSuffix>email</mat-icon>
                    <mat-error *ngIf="createForm.get('email')?.hasError('required')">
                      Contact email is required
                    </mat-error>
                    <mat-error *ngIf="createForm.get('email')?.hasError('email')">
                      Please enter a valid email address
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="step-actions">
                  <button mat-button routerLink="/login" type="button" class="back-btn">
                    <mat-icon>arrow_back</mat-icon>
                    Back to Login
                  </button>
                  <button mat-raised-button color="primary" type="button"
                          [disabled]="!isStep1Valid()"
                          (click)="nextStep()" class="next-btn">
                    Continue
                    <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </div>

              <!-- Step 2: Administrator Account -->
              <div class="step-content" *ngIf="currentStep === 2">
                <div class="step-header">
                  <h3>Administrator Account</h3>
                  <p>Create the primary administrator account for your school</p>
                </div>

                <div class="form-grid">
                  <div class="form-row">
                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>First Name</mat-label>
                      <input matInput formControlName="adminFirstName" placeholder="Enter first name">
                      <mat-icon matSuffix>person</mat-icon>
                      <mat-error *ngIf="createForm.get('adminFirstName')?.hasError('required')">
                        First name is required
                      </mat-error>
                      <mat-error *ngIf="createForm.get('adminFirstName')?.hasError('minlength')">
                        First name must be at least 2 characters
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="form-field">
                      <mat-label>Last Name</mat-label>
                      <input matInput formControlName="adminLastName" placeholder="Enter last name">
                      <mat-icon matSuffix>person</mat-icon>
                      <mat-error *ngIf="createForm.get('adminLastName')?.hasError('required')">
                        Last name is required
                      </mat-error>
                      <mat-error *ngIf="createForm.get('adminLastName')?.hasError('minlength')">
                        Last name must be at least 2 characters
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Admin Email</mat-label>
                    <input matInput type="email" formControlName="adminEmail" placeholder="<EMAIL>">
                    <mat-icon matSuffix>email</mat-icon>
                    <mat-error *ngIf="createForm.get('adminEmail')?.hasError('required')">
                      Admin email is required
                    </mat-error>
                    <mat-error *ngIf="createForm.get('adminEmail')?.hasError('email')">
                      Please enter a valid email address
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Username</mat-label>
                    <input matInput formControlName="adminUsername" placeholder="Enter username">
                    <mat-icon matSuffix>account_circle</mat-icon>
                    <mat-hint>Username for admin login (3-50 characters)</mat-hint>
                    <mat-error *ngIf="createForm.get('adminUsername')?.hasError('required')">
                      Username is required
                    </mat-error>
                    <mat-error *ngIf="createForm.get('adminUsername')?.hasError('minlength')">
                      Username must be at least 3 characters
                    </mat-error>
                    <mat-error *ngIf="createForm.get('adminUsername')?.hasError('pattern')">
                      Username can only contain letters, numbers, dots, hyphens, and underscores
                    </mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Password</mat-label>
                    <input matInput
                           [type]="hidePassword ? 'password' : 'text'"
                           formControlName="adminPassword"
                           placeholder="Enter secure password">
                    <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                      <mat-icon>{{hidePassword ? 'visibility' : 'visibility_off'}}</mat-icon>
                    </button>
                    <mat-hint>Strong password with uppercase, lowercase, number, and special character</mat-hint>
                    <mat-error *ngIf="createForm.get('adminPassword')?.hasError('required')">
                      Password is required
                    </mat-error>
                    <mat-error *ngIf="createForm.get('adminPassword')?.hasError('minlength')">
                      Password must be at least 8 characters
                    </mat-error>
                    <mat-error *ngIf="createForm.get('adminPassword')?.hasError('pattern')">
                      Password must contain uppercase, lowercase, number, and special character
                    </mat-error>
                  </mat-form-field>

                  <!-- Password Strength Indicator -->
                  <div class="password-strength" *ngIf="createForm.get('adminPassword')?.value">
                    <div class="strength-header">
                      <mat-icon class="strength-icon">security</mat-icon>
                      <span class="strength-title">Password Strength</span>
                    </div>
                    <div class="strength-requirements">
                      <div class="requirement"
                           [class.met]="hasMinLength(createForm.get('adminPassword')?.value || '')">
                        <mat-icon>{{hasMinLength(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                        <span>At least 8 characters</span>
                      </div>
                      <div class="requirement"
                           [class.met]="hasLowercase(createForm.get('adminPassword')?.value || '')">
                        <mat-icon>{{hasLowercase(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                        <span>One lowercase letter</span>
                      </div>
                      <div class="requirement"
                           [class.met]="hasUppercase(createForm.get('adminPassword')?.value || '')">
                        <mat-icon>{{hasUppercase(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                        <span>One uppercase letter</span>
                      </div>
                      <div class="requirement"
                           [class.met]="hasNumber(createForm.get('adminPassword')?.value || '')">
                        <mat-icon>{{hasNumber(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                        <span>One number</span>
                      </div>
                      <div class="requirement"
                           [class.met]="hasSpecialChar(createForm.get('adminPassword')?.value || '')">
                        <mat-icon>{{hasSpecialChar(createForm.get('adminPassword')?.value || '') ? 'check_circle' : 'radio_button_unchecked'}}</mat-icon>
                        <span>One special character</span>
                      </div>
                    </div>
                  </div>

                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Confirm Password</mat-label>
                    <input matInput
                           [type]="hidePasswordConfirm ? 'password' : 'text'"
                           formControlName="adminPasswordConfirm"
                           placeholder="Confirm your password">
                    <button mat-icon-button matSuffix (click)="hidePasswordConfirm = !hidePasswordConfirm" type="button">
                      <mat-icon>{{hidePasswordConfirm ? 'visibility' : 'visibility_off'}}</mat-icon>
                    </button>
                    <mat-error *ngIf="createForm.get('adminPasswordConfirm')?.hasError('required')">
                      Please confirm your password
                    </mat-error>
                    <mat-error *ngIf="createForm.hasError('passwordMismatch') && createForm.get('adminPasswordConfirm')?.touched">
                      Passwords do not match
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="step-actions">
                  <button mat-button type="button" (click)="previousStep()" class="back-btn">
                    <mat-icon>arrow_back</mat-icon>
                    Back
                  </button>
                  <button mat-raised-button color="primary" type="button"
                          [disabled]="!isStep2Valid()"
                          (click)="nextStep()" class="next-btn">
                    Continue
                    <mat-icon>arrow_forward</mat-icon>
                  </button>
                </div>
              </div>

              <!-- Step 3: Review & Submit -->
              <div class="step-content" *ngIf="currentStep === 3">
                <div class="step-header">
                  <h3>Review & Complete Setup</h3>
                  <p>Review your information and complete the setup</p>
                </div>

                <div class="review-section">
                  <div class="review-card">
                    <h4><mat-icon>school</mat-icon> School Information</h4>
                    <div class="review-item">
                      <span class="label">School Name:</span>
                      <span class="value">{{createForm.get('schoolName')?.value}}</span>
                    </div>
                    <div class="review-item">
                      <span class="label">School Type:</span>
                      <span class="value">{{getSchoolTypeLabel(createForm.get('schoolType')?.value)}}</span>
                    </div>
                    <div class="review-item">
                      <span class="label">URL:</span>
                      <span class="value">{{createForm.get('slug')?.value}}.edumanage.com</span>
                    </div>
                    <div class="review-item">
                      <span class="label">Contact Email:</span>
                      <span class="value">{{createForm.get('email')?.value}}</span>
                    </div>
                  </div>

                  <div class="review-card">
                    <h4><mat-icon>admin_panel_settings</mat-icon> Administrator</h4>
                    <div class="review-item">
                      <span class="label">Name:</span>
                      <span class="value">{{createForm.get('adminFirstName')?.value}} {{createForm.get('adminLastName')?.value}}</span>
                    </div>
                    <div class="review-item">
                      <span class="label">Email:</span>
                      <span class="value">{{createForm.get('adminEmail')?.value}}</span>
                    </div>
                    <div class="review-item">
                      <span class="label">Username:</span>
                      <span class="value">{{createForm.get('adminUsername')?.value}}</span>
                    </div>
                  </div>

                  <!-- Subscription Plan Selection -->
                  <div class="review-card">
                    <h4><mat-icon>payment</mat-icon> Subscription Plan</h4>
                    <div class="plan-selection">
                      <div class="plan-card"
                           [class.selected]="createForm.get('plan')?.value === 'trial'"
                           (click)="selectPlan('trial')">
                        <div class="plan-header">
                          <mat-icon class="plan-icon">free_breakfast</mat-icon>
                          <h5>Free Trial</h5>
                          <div class="plan-price">Free</div>
                          <div class="plan-duration">30 days</div>
                        </div>
                        <div class="plan-features">
                          <div class="feature"><mat-icon>check</mat-icon> Up to 100 students</div>
                          <div class="feature"><mat-icon>check</mat-icon> Basic features</div>
                          <div class="feature"><mat-icon>check</mat-icon> Email support</div>
                          <div class="feature"><mat-icon>check</mat-icon> 1GB storage</div>
                        </div>
                      </div>

                      <div class="plan-card"
                           [class.selected]="createForm.get('plan')?.value === 'basic'"
                           (click)="selectPlan('basic')">
                        <div class="plan-header">
                          <mat-icon class="plan-icon">star</mat-icon>
                          <h5>Basic Plan</h5>
                          <div class="plan-price">$29</div>
                          <div class="plan-duration">per month</div>
                        </div>
                        <div class="plan-features">
                          <div class="feature"><mat-icon>check</mat-icon> Up to 500 students</div>
                          <div class="feature"><mat-icon>check</mat-icon> All features</div>
                          <div class="feature"><mat-icon>check</mat-icon> Priority support</div>
                          <div class="feature"><mat-icon>check</mat-icon> 10GB storage</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="step-actions">
                  <button mat-button type="button" (click)="previousStep()" class="back-btn">
                    <mat-icon>arrow_back</mat-icon>
                    Back
                  </button>
                  <button mat-raised-button color="primary"
                          [disabled]="!createForm.valid || isLoading"
                          (click)="onSubmit()" class="submit-btn">
                    <mat-spinner *ngIf="isLoading" diameter="20" class="button-spinner"></mat-spinner>
                    <mat-icon *ngIf="!isLoading">rocket_launch</mat-icon>
                    <span *ngIf="!isLoading">Create School</span>
                    <span *ngIf="isLoading">Creating...</span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
      min-height: 100vh;
    }

    .tenant-setup-container {
      height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      box-sizing: border-box;
      overflow-y: auto;
    }

    .setup-wizard {
      background: white;
      border-radius: 24px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      max-width: 900px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }

    .progress-section {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
      flex-shrink: 0;
    }

    .progress-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .progress-header h2 {
      font-size: 24px;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 6px 0;
    }

    .progress-header p {
      color: #64748b;
      font-size: 14px;
      margin: 0;
    }

    .step-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0;
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      opacity: 0.5;
      transition: all 0.3s ease;
    }

    .step.active {
      opacity: 1;
    }

    .step.completed {
      opacity: 1;
    }

    .step-circle {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: #e2e8f0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      color: #64748b;
      transition: all 0.3s ease;
    }

    .step.active .step-circle {
      background: #3b82f6;
      color: white;
    }

    .step.completed .step-circle {
      background: #10b981;
      color: white;
    }

    .step-content {
      text-align: center;
    }

    .step-title {
      font-weight: 600;
      color: #1e293b;
      font-size: 14px;
      display: block;
    }

    .step-description {
      color: #64748b;
      font-size: 12px;
      display: block;
    }

    .step-connector {
      width: 80px;
      height: 2px;
      background: #e2e8f0;
      transition: all 0.3s ease;
    }

    .step-connector.active {
      background: #10b981;
    }
    
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .form-container {
      padding: 24px;
      flex: 1;
      overflow-y: auto;
    }

    .step-content {
      display: block;
    }

    .step-header {
      text-align: center;
      margin-bottom: 24px;
    }

    .step-header h3 {
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 6px 0;
    }

    .step-header p {
      color: #64748b;
      font-size: 14px;
      margin: 0;
    }

    .form-grid {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .form-field {
      flex: 1;
      min-width: 0;
    }

    mat-form-field {
      width: 100%;
    }

    mat-form-field.full-width {
      width: 100%;
    }
    
    .review-section {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .review-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 24px;
      border: 1px solid #e2e8f0;
    }

    .review-card h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }

    .review-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #e2e8f0;
    }

    .review-item:last-child {
      border-bottom: none;
    }

    .review-item .label {
      font-weight: 500;
      color: #64748b;
    }

    .review-item .value {
      font-weight: 600;
      color: #1e293b;
    }

    .plan-selection {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 16px;
    }

    .plan-card {
      border: 2px solid #e2e8f0;
      border-radius: 16px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: white;
    }

    .plan-card:hover {
      border-color: #3b82f6;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    }

    .plan-card.selected {
      border-color: #3b82f6;
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    }

    .plan-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .plan-icon {
      font-size: 32px;
      color: #3b82f6;
      margin-bottom: 8px;
    }

    .plan-card h5 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 700;
      color: #1e293b;
    }

    .plan-price {
      font-size: 24px;
      font-weight: 700;
      color: #3b82f6;
      margin-bottom: 4px;
    }

    .plan-duration {
      color: #64748b;
      font-size: 14px;
    }

    .plan-features {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .feature {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #1e293b;
    }

    .feature mat-icon {
      color: #10b981;
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
    
    mat-card-actions {
      display: flex;
      align-items: center;
      padding: 16px 24px;
    }
    
    .spacer {
      flex: 1;
    }
    
    .button-spinner {
      margin-right: 8px;
    }
    
    .password-strength {
      margin: 12px 0;
      padding: 16px;
      border-radius: 12px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border: 1px solid #e2e8f0;
    }

    .strength-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }

    .strength-icon {
      color: #3b82f6;
      font-size: 20px;
    }

    .strength-title {
      font-weight: 600;
      color: #1e293b;
      font-size: 14px;
    }

    .strength-requirements {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .requirement {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #64748b;
      transition: all 0.2s ease;
    }

    .requirement.met {
      color: #10b981;
      font-weight: 500;
    }

    .requirement mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .requirement.met mat-icon {
      color: #10b981;
    }

    .step-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #e2e8f0;
    }

    .back-btn {
      color: #64748b;
    }

    .next-btn, .submit-btn {
      min-width: 140px;
      height: 48px;
      border-radius: 12px;
      font-weight: 600;
      text-transform: none;
      font-size: 16px;
    }

    .button-spinner {
      margin-right: 8px;
    }

    @media (max-width: 768px) {
      .tenant-setup-container {
        padding: 16px;
        height: 100vh;
      }

      .setup-wizard {
        border-radius: 16px;
        max-height: 95vh;
      }

      .progress-section {
        padding: 16px;
      }

      .form-container {
        padding: 16px;
      }

      .step-indicator {
        flex-direction: column;
        gap: 16px;
      }

      .step {
        flex-direction: row;
        gap: 12px;
      }

      .step-connector {
        display: none;
      }

      .form-row {
        flex-direction: column;
        gap: 16px;
      }

      .plan-selection {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .strength-requirements {
        grid-template-columns: 1fr;
        gap: 8px;
      }

      .step-actions {
        flex-direction: column;
        gap: 16px;
      }

      .step-actions button {
        width: 100%;
        order: 2;
      }

      .back-btn {
        order: 1;
      }
    }

    @media (max-width: 480px) {
      .tenant-setup-container {
        padding: 12px;
        height: 100vh;
      }

      .setup-wizard {
        border-radius: 12px;
        max-height: 98vh;
      }

      .progress-section {
        padding: 12px;
      }

      .form-container {
        padding: 12px;
      }

      .progress-header h2 {
        font-size: 20px;
      }

      .step-header h3 {
        font-size: 18px;
      }

      .form-grid {
        gap: 12px;
      }

      .step-actions {
        margin-top: 16px;
        padding-top: 12px;
      }
    }
  `]
})
export class TenantCreateComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);
  private authService = inject(AuthService);
  private tenantService = inject(TenantManagementService);

  createForm!: FormGroup;
  isLoading = false;
  slug = '';
  hidePassword = true;
  hidePasswordConfirm = true;
  currentStep = 1;

  ngOnInit() {
    this.createForm = this.fb.group({
      // School Information
      schoolName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      slug: ['', [Validators.required, Validators.pattern(/^[a-z0-9-]+$/), Validators.minLength(2), Validators.maxLength(50)]],
      displayName: [''],
      schoolType: ['', Validators.required],
      country: ['BD', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      customDomain: ['', [Validators.pattern(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/)]],
      language: ['en-US', Validators.required],
      timeZone: ['Asia/Dhaka', Validators.required],
      currency: ['USD', Validators.required],

      // Admin User Information
      adminFirstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      adminLastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      adminEmail: ['', [Validators.required, Validators.email]],
      adminUsername: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(50), Validators.pattern(/^[a-zA-Z0-9_.-]+$/)]],
      adminPassword: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      ]],
      adminPasswordConfirm: ['', Validators.required],

      // Subscription Plan
      plan: ['trial', Validators.required]
    }, {
      validators: this.passwordMatchValidator
    });

    // Auto-generate slug from school name
    this.createForm.get('schoolName')?.valueChanges.subscribe(name => {
      if (name) {
        const generatedSlug = name.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();
        this.createForm.get('slug')?.setValue(generatedSlug);
        this.slug = generatedSlug;
      }
    });

    // Auto-generate display name from school name if not provided
    this.createForm.get('schoolName')?.valueChanges.subscribe(name => {
      const displayName = this.createForm.get('displayName');
      if (name && !displayName?.value) {
        displayName?.setValue(name);
      }
    });

    // Auto-generate admin username from admin email
    this.createForm.get('adminEmail')?.valueChanges.subscribe(email => {
      const username = this.createForm.get('adminUsername');
      if (email && !username?.value) {
        const generatedUsername = email.split('@')[0].toLowerCase()
          .replace(/[^a-z0-9._-]/g, '');
        username?.setValue(generatedUsername);
      }
    });
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  // Custom validator for password confirmation
  passwordMatchValidator(form: FormGroup) {
    const password = form.get('adminPassword');
    const confirmPassword = form.get('adminPasswordConfirm');

    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  // Helper method to check if password meets requirements
  getPasswordErrors(): string[] {
    const password = this.createForm.get('adminPassword')?.value || '';
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('At least 8 characters');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('One lowercase letter');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('One uppercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('One number');
    }
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('One special character (@$!%*?&)');
    }

    return errors;
  }

  // Check if password is strong enough
  isPasswordStrong(): boolean {
    return this.getPasswordErrors().length === 0;
  }

  // Helper methods for password validation
  hasMinLength(password: string): boolean {
    return password.length >= 8;
  }

  hasLowercase(password: string): boolean {
    return /[a-z]/.test(password);
  }

  hasUppercase(password: string): boolean {
    return /[A-Z]/.test(password);
  }

  hasNumber(password: string): boolean {
    return /\d/.test(password);
  }

  hasSpecialChar(password: string): boolean {
    return /[@$!%*?&]/.test(password);
  }

  // Wizard navigation methods
  nextStep() {
    if (this.currentStep < 3) {
      this.currentStep++;
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  // Step validation methods
  isStep1Valid(): boolean {
    const step1Fields = ['schoolName', 'slug', 'schoolType', 'country', 'email'];
    return step1Fields.every(field => {
      const control = this.createForm.get(field);
      return control && control.valid;
    });
  }

  isStep2Valid(): boolean {
    const step2Fields = ['adminFirstName', 'adminLastName', 'adminEmail', 'adminUsername', 'adminPassword', 'adminPasswordConfirm'];
    const fieldsValid = step2Fields.every(field => {
      const control = this.createForm.get(field);
      return control && control.valid;
    });
    return fieldsValid && !this.createForm.hasError('passwordMismatch');
  }

  // Helper method to get school type label
  getSchoolTypeLabel(value: string): string {
    const types: { [key: string]: string } = {
      'PrimarySchool': 'Primary School',
      'SecondarySchool': 'Secondary School',
      'School': 'Combined School (K-12)',
      'College': 'College',
      'University': 'University',
      'TechnicalInstitute': 'Technical Institute',
      'Academy': 'Academy',
      'InternationalSchool': 'International School',
      'OnlineSchool': 'Online School'
    };
    return types[value] || value;
  }

  selectPlan(plan: string) {
    this.createForm.get('plan')?.setValue(plan);
  }

  onSubmit() {
    if (this.createForm.valid) {
      this.isLoading = true;

      const formValue = this.createForm.value;

      // Prepare tenant creation request
      const tenantRequest: CreateTenantRequest = {
        name: formValue.schoolName,
        slug: formValue.slug,
        displayName: formValue.displayName || formValue.schoolName,
        type: formValue.schoolType || 'School',
        customDomain: formValue.customDomain,
        defaultLanguage: formValue.language || 'en-US',
        timeZone: formValue.timeZone || 'Asia/Dhaka',
        currency: formValue.currency || 'USD',
        isTrialActive: true,
        trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days trial
        adminUser: {
          firstName: formValue.adminFirstName,
          lastName: formValue.adminLastName,
          email: formValue.adminEmail,
          username: formValue.adminUsername || formValue.adminEmail,
          password: formValue.adminPassword
        }
      };

      // Call the tenant creation API
      this.tenantService.createTenant(tenantRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open('School created successfully! Redirecting to setup...', 'Close', { duration: 3000 });

          // Redirect to setup wizard with the new tenant
          this.router.navigate(['/tenant-setup/wizard'], {
            queryParams: { tenant: tenantRequest.slug }
          });
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error creating tenant:', error);

          let errorMessage = 'Failed to create school. Please try again.';
          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          this.snackBar.open(errorMessage, 'Close', { duration: 5000 });
        }
      });
    }
  }
}
