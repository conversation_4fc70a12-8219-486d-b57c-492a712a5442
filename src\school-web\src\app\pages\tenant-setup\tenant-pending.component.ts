import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-tenant-pending',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    RouterModule,
    TranslateModule
  ],
  template: `
    <div class="tenant-pending-container">
      <mat-card class="pending-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>hourglass_empty</mat-icon>
            School Setup Pending
          </mat-card-title>
          <mat-card-subtitle>
            Your school setup is not yet complete
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="pending-info">
            <div class="info-section">
              <mat-icon class="section-icon">admin_panel_settings</mat-icon>
              <div class="section-content">
                <h4>Administrator Setup Required</h4>
                <p>Only school administrators can complete the initial setup process. This includes:</p>
                <ul>
                  <li>School profile configuration</li>
                  <li>Academic structure setup</li>
                  <li>User roles and permissions</li>
                  <li>System settings configuration</li>
                </ul>
              </div>
            </div>

            <div class="info-section">
              <mat-icon class="section-icon">schedule</mat-icon>
              <div class="section-content">
                <h4>What happens next?</h4>
                <p>Once your school administrator completes the setup:</p>
                <ul>
                  <li>You'll receive access to your appropriate portal</li>
                  <li>All school features will become available</li>
                  <li>You can start using the system immediately</li>
                </ul>
              </div>
            </div>

            <div class="info-section">
              <mat-icon class="section-icon">contact_support</mat-icon>
              <div class="section-content">
                <h4>Need Help?</h4>
                <p>If you believe you should have administrator access, please contact your system administrator or IT support.</p>
              </div>
            </div>
          </div>

          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/login">
              <mat-icon>refresh</mat-icon>
              Check Setup Status
            </button>
            <button mat-button routerLink="/login">
              <mat-icon>logout</mat-icon>
              Sign Out
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .tenant-pending-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      padding: 20px;
      z-index: 9999;
      overflow-y: auto;
    }
    .pending-card {
      max-width: 700px;
      width: 100%;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .pending-info {
      margin: 24px 0;
      text-align: left;
    }

    .info-section {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 24px;
      padding: 16px;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.1);
    }

    .section-icon {
      font-size: 24px;
      width: 32px;
      height: 32px;
      color: #fff;
      flex-shrink: 0;
    }

    .section-content {
      flex: 1;
    }

    .section-content h4 {
      margin: 0 0 8px 0;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }

    .section-content p {
      margin: 0 0 8px 0;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.5;
    }

    .section-content ul {
      margin: 8px 0 0 0;
      padding-left: 20px;
      color: rgba(255, 255, 255, 0.9);
    }

    .section-content li {
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .actions {
      margin-top: 32px;
      text-align: center;
      display: flex;
      gap: 16px;
      justify-content: center;
    }
  `]
})
export class TenantPendingComponent {}
