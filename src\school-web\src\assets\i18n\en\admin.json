{"admin": {"title": "Admin Panel", "welcome": "Welcome to the Admin Panel", "categories": {"main": "Main", "content": "Content Management", "academic": "Academic", "campus": "Campus Life", "users": "Users & Staff", "system": "System Management", "platform": "Platform Management", "settings": "Settings"}, "menu": {"dashboard": "Dashboard", "notices": "Notices", "news": "News & Announcements", "events": "Events", "pages": "Pages", "media": "Media Library", "faculty": "Faculty Profiles", "courses": "Courses & Curriculum", "departments": "Departments", "admissions": "Admissions", "facilities": "Facilities", "hostel": "Hostel Management", "transportation": "Transportation", "students": "Students", "parents": "Parents", "staff": "Staff", "alumni": "Alumni", "users": "User Accounts", "general": "General Settings", "appearance": "Appearance", "translations": "Translations", "academic_years": "Academic Years", "holidays": "Holidays", "calendar": "Calendar", "grades": "Grades", "sections": "Sections", "subjects": "Subjects", "class_teachers": "Class Teachers", "terms": "Terms", "tenants": "Tenant Management", "organizations": "Organizations", "system_users": "System Users", "system_logs": "System Logs", "subscriptions": "Subscriptions", "licenses": "Licenses", "analytics": "Analytics", "monitoring": "System Monitoring"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to the Admin Dashboard", "stats": "Quick Stats", "recent_activity": "Recent Activity", "pending_tasks": "Pending Tasks"}, "notices": {"title": "Manage Notices", "create": "Create Notice", "edit": "Edit Notice", "view": "View Notice", "delete": "Delete Notice", "list": "All Notices", "search": "Search notices", "search_placeholder": "Search by title or content", "category": "Category", "status": "Status", "no_notices": "No notices found", "title_column": "Title", "category_column": "Category", "start_date": "Start Date", "end_date": "End Date", "priority": "Priority", "no_end_date": "No end date", "title_field": "Title", "content_field": "Content", "category_field": "Category", "priority_field": "Priority", "start_date_field": "Start Date", "end_date_field": "End Date", "end_date_hint": "Leave empty for notices without an end date", "end_date_before_start_date": "End date must be after start date", "is_active": "Active Notice", "content": "Content", "created_at": "Created At", "updated_at": "Updated At", "create_message": "Fill in the notice details and add translations if needed. Only the English version is required.", "edit_message": "Edit the notice details and translations. Changes will be saved when you click Update."}, "content": {"title": "Manage Content", "create": "Create Content", "edit": "Edit Content", "delete": "Delete Content", "list": "All Content"}, "media": {"title": "Media Library", "upload": "Upload Media", "uploadMedia": "Upload Media Files", "uploadNew": "Upload New", "delete": "Delete Media", "deleteTitle": "Delete Media Item", "deleteConfirm": "Are you sure you want to delete the media item \"{{fileName}}\"?", "deleteSuccess": "Media item deleted successfully", "deleteError": "Failed to delete media item", "list": "All Media", "search": "Search media", "searchPlaceholder": "Search by file name or caption", "type": "Media Type", "types": {"image": "Image", "document": "Document", "video": "Video", "audio": "Audio", "other": "Other"}, "noMedia": "No media items found", "selectFiles": "Select Files", "selectFilesHint": "Select one or more files to upload", "selectedFiles": "Selected Files", "typeRequired": "Media type is required", "altText": "Alt Text", "altTextHint": "Describe the media for accessibility (required for images)", "caption": "Caption", "captionHint": "Add a caption to describe this media", "uploadSuccess": "Media uploaded successfully", "uploadError": "Failed to upload media", "backToLibrary": "Back to Media Library", "originalFileName": "Original File Name", "mimeType": "MIME Type", "uploadedBy": "Uploaded By", "uploadedAt": "Uploaded At", "url": "URL", "copyUrl": "Copy URL", "copiedToClipboard": "URL copied to clipboard", "open": "Open in New Tab", "loadError": "Failed to load media items"}, "tenants": {"title": "Tenant Management", "subtitle": "Manage school organizations and their access", "create": "Create Tenant", "create_subtitle": "Create a new school organization with admin user", "edit": "Edit Tenant", "edit_subtitle": "Update tenant information and settings", "create_first": "Create First Tenant", "no_tenants": "No Tenants Found", "no_tenants_message": "No school organizations have been created yet. Create your first tenant to get started.", "name": "Organization Name", "slug": "Slug", "display_name": "Display Name", "type": "Organization Type", "status": "Status", "status_filter": "Filter by Status", "users": "Users", "trial": "Trial", "created": "Created Date", "search": "Search tenants", "custom_domain": "Custom Domain", "localization": "Localization Settings", "localization_subtitle": "Configure language, timezone, and currency settings", "language": "Default Language", "timezone": "Time Zone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "trial_settings": "Trial Settings", "trial_subtitle": "Configure trial period and limitations", "enable_trial": "Enable Trial Period", "trial_end_date": "Trial End Date", "admin_user": "Administrator User", "admin_user_subtitle": "Create the initial administrator for this tenant", "first_name": "First Name", "last_name": "Last Name", "email": "Email Address", "username": "Username", "password": "Password", "basic_info": "Basic Information", "basic_info_subtitle": "Configure the fundamental details of the organization"}, "users": {"title": "Manage Users", "create": "Create User", "edit": "Edit User", "delete": "Delete User", "list": "All Users"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "apply_filter": "Apply Filter", "reset": "Reset", "actions": "Actions", "status": "Status", "created": "Created", "updated": "Updated", "published": "Published", "draft": "Draft", "active": "Active", "inactive": "Inactive", "error_loading": "Error loading data. Please try again.", "retry": "Retry", "create": "Create", "update": "Update", "field_required": "This field is required", "max_length": "Maximum length is {{length}} characters"}, "academic_years": {"title": "Academic Years", "create": "Create Academic Year", "edit": "Edit Academic Year", "view": "View Academic Year", "delete": "Delete Academic Year", "list": "All Academic Years", "search": "Search academic years", "name": "Academic Year Name", "start_date": "Start Date", "end_date": "End Date", "is_current": "Current Year", "description": "Description", "status": "Status", "terms": "Terms", "add_term": "Add Term", "term_name": "Term Name", "term_start": "Term Start Date", "term_end": "Term End Date"}, "holidays": {"title": "Holidays", "create": "Create Holiday", "edit": "Edit Holiday", "view": "View Holiday", "delete": "Delete Holiday", "list": "All Holidays", "search": "Search holidays", "name": "Holiday Name", "description": "Description", "start_date": "Start Date", "end_date": "End Date", "type": "Holiday Type", "is_recurring": "Recurring Holiday", "recurrence_pattern": "Recurrence <PERSON>", "color": "Color", "is_active": "Active", "is_public": "Public Holiday", "academic_year": "Academic Year", "term": "Term", "translations": "Translations", "add_translation": "Add Translation", "language": "Language", "types": {"national": "National Holiday", "religious": "Religious Holiday", "cultural": "Cultural Holiday", "academic": "Academic Holiday", "other": "Other"}}, "calendar": {"title": "Calendar", "view": "Calendar View", "month": "Month", "week": "Week", "day": "Day", "today": "Today", "previous": "Previous", "next": "Next", "events": "Events", "holidays": "Holidays", "academic_events": "Academic Events", "filters": "Filters", "show_holidays": "Show Holidays", "show_academic_events": "Show Academic Events", "show_public_only": "Public Events Only", "event_types": "Event Types", "statistics": "Statistics", "total_events": "Total Events", "upcoming_events": "Upcoming Events", "current_month": "This Month"}}}